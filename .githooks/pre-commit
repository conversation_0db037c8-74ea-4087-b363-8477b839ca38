#!/bin/sh -e
set -e

# Reference: https://github.com/a1exsh/google-java-format-git-pre-commit-hook
RELEASE=1.7
JAR_NAME="google-java-format-${RELEASE}-all-deps.jar"
RELEASES_URL=https://github.com/google/google-java-format/releases/download
JAR_URL="${RELEASES_URL}/google-java-format-${RELEASE}/${JAR_NAME}"

CACHE_DIR="$HOME/.cache/google-java-format-git-pre-commit-hook"
JAR_FILE="$CACHE_DIR/$JAR_NAME"
JAR_DOWNLOAD_FILE="${JAR_FILE}.tmp"

if [[ ! -f "$JAR_FILE" ]]
then
    mkdir -p "$CACHE_DIR"
    curl -L "$JAR_URL" -o "$JAR_DOWNLOAD_FILE"
    mv "$JAR_DOWNLOAD_FILE" "$JAR_FILE"
fi

changed_java_files=$(git diff --cached --name-only --diff-filter=ACMR | grep ".*java$" || true)
if [[ -n "$changed_java_files" ]]
then
    echo "Reformatting Java files: $changed_java_files"
    if ! java -jar "$JAR_FILE" --replace --set-exit-if-changed $changed_java_files
    then
        echo "Some files were changed, aborting commit!" >&2
        exit 1
    fi
else
    echo "No Java files changes found."
fi
