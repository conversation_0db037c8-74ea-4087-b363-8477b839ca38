## Description

This MR includes automation for the following <put something here> that executes within the pipeline as part of the regression run.

## Pipeline Verification
- [ ] All Tests must pass over, or at least 3 runs from a fresh environment

## Checklist:

- [ ] My test plan has been approved
- [ ] All non-e2e test cases that require data use pre-seeded data
- [ ] My data seeding has been defined based on the endpoint DB requirements
- [ ] My data seeding scripts begin with the deletion of old data seeded previously for the specific data seeding run
- [ ] Data seeding is executed in the BeforeClass method for each Test Class
- [ ] All test data is stored using JSON by the pre-defined formats
- [ ] All automated tests must match the tests from the test plan (count, request type, authentication, HTTP status, etc.)
- [ ] All automated tests rely on the DTO for sending the request and validating the response
- [ ] At a minimum all test cases must validate the HTTP status code of the response
- [ ] All automated test cases pass unless there is an open defect
- [ ] All automated test cases that do not include a response body must include a comment for future reference

