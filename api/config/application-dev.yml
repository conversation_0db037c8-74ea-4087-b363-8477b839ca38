KEY_STORE: sca-scanresult-service-keystore.jks
TRUST_STORE: truststore-qa.jks
KEY_STORE_PASSWORD: changeit
TRUST_STORE_PASSWORD: changeit
DATABASE_URL_SCANRESULT: ************************************************************
DATABASE_USER: scauser
DATABASE_PASSWORD: scauser

#If test against stage-152, set false for ENABLE_FLYWAY
#If develop db migration SQL script (for local), set true for ENABLE_FLYWAY
ENABLE_FLYWAY: false

ENABLE_SWAGGER: true
RESULT_READY_Q_LISTENER_ENABLED: false
DATA_UPDATE_Q_LISTENER_ENABLED: false
REGISTRY_API_URL: https://registry-api.stage.srcclr.io
PLATFORM_API_URL: https://api.stage.srcclr.io
APPLICATION_SERVICES_POLICY_BACKEND_URL: https://localhost:12701
PLATFORM_SIGN_KEY_FILE: sca-scanresult-jwt-sign-qa.pem
application.environment: dev
SSL_CLIENT_AUTH: want

debug: true

# disabling HTTPS for local development
server:
  ssl:
    enabled: false

cloud:
  aws:
    credentials:
      instanceProfile: false
      #accessKey: xxxx
      #secretKey: xxxx
    region:
      static: us-east-1

application:
  environment: dev
  jms:
    enabled: true
    sns.enabled: true
    scan-event:
      enabled: true
    result-ready:
      enabled: false
      queue: ${RESULT_READY_Q:scan-result-q}
    data-update:
      enabled: false
      queue: ${DATA_UPDATE_Q:data-update-q}
    policy-event:
      enabled: false
      queue: ${application.environment}-policy-event
    scan-update-event:
      enabled: true
      queue: ${application.environment}-sca-scan-event-srm

spring:
  jpa:
    properties:
      hibernate:
        show_sql: true
        format_sql: true
  # limiting the number of local Hikari connections
  datasource:
    hikari:
      maximum-pool-size: 2

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql: TRACE
    org.springframework.jdbc.core: TRACE
    # Logging level needed to debug cache behavior in dev environment
    org.springframework.data.*.*: trace
    org.springframework.cache.*: trace

findings:
  legacy-query: false

redis:
  cacheables:
    exploitability:
      enabled: true
      expiration: 60  # 60s for development testing cache lifetime
    findings:
      enabled: true
      expiration: 300
    findings-with-nvd:
      enabled: true
      expiration: 300
