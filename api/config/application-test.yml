KEY_STORE: sca-scanresult-service-keystore.jks
TRUST_STORE: truststore-qa.jks
KEY_STORE_PASSWORD: changeit
TRUST_STORE_PASSWORD: changeit
DATABASE_USER: sa

cloud:
  aws:
    credentials:
      instanceProfile: false

spring:
  datasource:
    driverClassName: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=Oracle
    continue-on-error: true
    username: sa
    password:
  jpa:
    properties:
      hibernate:
        hbm2ddl:
          auto: create-drop
  flyway:
    sql-migration-prefix: "V"

application:
  jms:
    enabled: false
    result-ready:
      enabled: false
    data-update:
      enabled: false
