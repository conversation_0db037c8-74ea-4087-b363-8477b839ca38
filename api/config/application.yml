#=======================================================================================================================
# System properties
#=======================================================================================================================

# Tomcat SSL configuration
server:
  max-http-header-size: 64KB
  port: 8509
  servlet:
    context-path: /sca/scanresult
  ssl:
    enabled: true
    key-store: "file:config/${KEY_STORE}"
    key-store-password: ${KEY_STORE_PASSWORD}
    trust-store: "file:config/${TRUST_STORE}"
    trust-store-password: ${TRUST_STORE_PASSWORD}
    key-alias: sca-scanresult-service
    protocol: TLS
    enabled-protocols: TLSv1.2
    client-auth: ${SSL_CLIENT_AUTH:need}
    ciphers: >
      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
      TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,
      TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384,
      TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
      TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
      TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,
      TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,
      TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,
      TLS_DHE_RSA_WITH_AES_256_GCM_SHA384,
      TLS_DHE_RSA_WITH_AES_128_CBC_SHA256
  compression:
    enabled: true
    mime-types: application/json
mtls:
  enabled: false
#=======================================================================================================================
# agora properties
#=======================================================================================================================
agora:
  client:
    ssl:
      # Keystore that contains the client cert/key pair
      key-store: ${server.ssl.key-store}
      key-store-password: ${server.ssl.key-store-password}
      #Scaserv1
      # Truststore used to verify server cert
      trust-store: ${server.ssl.trust-store}
      trust-store-password: ${server.ssl.trust-store-password}
      trust-all: false
  server:
    ssl:
      # This property lists the services that are allowed to call this service.
      clients-allowed: ${CLIENTS_ALLOWED:*}
  security:
    principal:
      jwt.public.file: config/agora-identityservice-principal-signing-%s.pem
      unsigned:
        # Running in data center called by monolith so we support unsigned principal for authentication
        enabled: true
    service:
      authentication: false
  message:
    env: agora-stage-3
    queue:
      apiAnnotation: # fifo queue name
        queue: ${agora.message.env}_sca-annotation-changed-q.fifo
  sandbox-fixed-date: 2018-10-27 0:0:0.0

#=======================================================================================================================
# Application properties
#======================================================================================================================= 
spring:
  application:
    name: sca-scanresult-service
  main:
    allow-bean-definition-overriding: true
  cache:
    type: redis
  datasource:
    read-write-db:
      driverClassName: org.postgresql.Driver
      url: ${DATABASE_URL_SCANRESULT}
      username: ${DATABASE_USER}
      password: ${DATABASE_PASSWORD}
      keyfile: file:config/dbkeyfile
      hikari:
        maximum-pool-size: 20
        connection-init-sql: SELECT 1
    read-only-db:
      driverClassName: org.postgresql.Driver
      url: ${DATABASE_URL_SCANRESULT}
      username: ${DATABASE_USER}
      password: ${DATABASE_PASSWORD}
      keyfile: file:config/dbkeyfile
      hikari:
        maximum-pool-size: 20
        connection-init-sql: SELECT 1
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQL9Dialect
    properties:
      hibernate:
        naming_strategy: org.hibernate.cfg.ImprovedNamingStrategy
        show_sql: false
        jdbc:
          use_get_generated_keys: true
        temp:
          use_jdbc_metadata_defaults: false
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL
    mapper:
      default-view-inclusion: true 
    deserialization:
      READ_UNKNOWN_ENUM_VALUES_AS_NULL: false
  # ATTN: spring.data.rest.* is PART OF THE API CONTRACT WE KEEP WITH CLIENTS, do NOT change it recklessly!
  data:
    rest:
      max-page-size: 500
      default-page-size: 100
  flyway:
    enabled: ${ENABLE_FLYWAY:true}
    check-location: true
    locations:
      - classpath:db/migration
    migrate: ${ENABLE_FLYWAY:true}
    repair: false
    baseline-on-migrate: true
    table: schema_flyway_scanresult
    schemas: ${spring.datasource.read-write-db.username}
    out-of-order: true
    sql-migration-prefix: ""

springfox:
  documentation:
    swagger:
      v2:
        path: /api-docs
      enabled: ${ENABLE_SWAGGER:false}

cloud:
  aws:
    credentials:
      instanceProfile: true
    region:
      static: us-east-1
    stack:
      auto: false

logging:
  level:
    root: INFO
    com.veracode.agora.servicebase.auth: ERROR
    com.zaxxer.hikari.pool.HikariPool: DEBUG
  file: logs/${spring.application.name}.log

management:
  health:
    redis:
      enabled: false # Excluding redis from Health check since it's not required for the app to work
  server:
    port: 8609
    ssl:
      enabled: false
    servlet:
      context-path: ${server.servlet.context-path}
  endpoints:
    web:
      exposure:
        include: "*"
    cache:
      enabled: true # Used to manage caches (please refer to the README.md)
  endpoint:
    health:
      show-details: always

application:
  environment: ${POD_NAMESPACE}
  events:
    scan:
      notify-agora: true
  jms:
    enabled: ${JMS_ENABLED:true}
    sns.enabled: false
    result-ready:
      enabled: ${RESULT_READY_Q_LISTENER_ENABLED:true}
      queue: q-scan-result-${application.environment}
    data-update:
      enabled: ${DATA_UPDATE_Q_LISTENER_ENABLED:true}
      queue: q-data-update-${application.environment}
    scan-event:
      enabled: false
      topic: ${application.environment}_sca-scan-event
    policy-event:
      enabled: ${DATA_POLICY_EVENT_Q_LISTENER_ENABLED:true}
      queue: ${application.environment}-policy-event
    scan-update-event:
      enabled: true
      queue: ${application.environment}-sca-scan-event-srm
  scan:
    timeout: 360
  dummy-data:
    enabled: ${DUMMY_DATA_ENABLED:false}
    account-ids:
      - 101
      - 102
  blacklist:
    result:
      page-size: 250
  services:
    policy-backend-url: https://localhost:12701
  scheduled:
    job:
      enabled: false
      process-alerts:
        least-timeout: 5s
        most-timeout: 3m
        batch-size: 30
        cron: "0 0 0 * * *"

srcclr:
  registry-api-url: ${REGISTRY_API_URL}

platform:
  plt-jwt-signing-key: file:config/${PLATFORM_SIGN_KEY_FILE}
  plt-api-url: ${PLATFORM_API_URL}
  user-agent: Veracode-SCA/1.0

redis:
  cacheables:
    exploitability:
      enabled: true
      expiration: 86400 # 24 hours (base data changes once a day)
    findings:
      enabled: true
      expiration: 900 # 15 minutes (short-lived, allowing for enough time to fetch different pages)
    findings-with-nvd:
      enabled: true
      expiration: 900 # 15 minutes (short-lived, allowing for enough time to fetch different pages)

