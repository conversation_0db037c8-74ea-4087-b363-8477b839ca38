delete from account_blacklist where component_id like 'cccc0000-%';
delete from mitigation where component_id like 'cccc0000-%';
delete from component_cve where component_id like 'cccc0000-%';
delete from scan_component_path where scan_component_id like 'scancomponent-%';
delete from scan_component where scan_component_id like 'scancomponent-%';
delete from scan_finding where scan_id like 'bbbb0000-%';
delete from finding where product_id like 'product0-%';
delete from scan where scan_id like 'bbbb0000-%';
delete from cve where cve_id like 'CVE-0000-%';
delete from component where component_id like 'cccc0000-%';
delete from product_license where product_license_id like 'prodlice-%';
delete from product where product_id like 'product0-%';
<#-- delete from artifact_license_status where artifact_id like 'artifact-%'; -->
delete from artifact where artifact_id like 'artifact-%';
delete from artifact_file_path where artifact_id like 'artifact-%';
delete from artifact_cve where artifact_id like 'artifact-%';
delete from scan where server = 'mock_data_scan_result';
delete from scan where scan_config_id like 'srsCreSn%';
<#-- next 3 lines USED BY DEV QUERY_VIEW TESTING DO NOT MODIFY OR DELETE -->
delete from project_scan_component where project_scan_id like '0bbb0000-%';
delete from project_scan where project_scan_id like '0bbb0000-%';
delete from app_project where app_project_id like 'aabb0000-%';

<#-- adding products -->
<#assign prodref='product0-0000-4000-8000-**********0'>
<#-- adding product licenses -->
<#assign prodlicref='prodlice-0000-4000-8000-**********0'>
<#-- adding components -->
<#assign compref='cccc0000-0000-4000-8000-**********0'>
<#-- adding scans -->
<#assign scanpref='bbbb0000-0000-4000-8000-**********0'>
<#-- DO NOT MODIFY DELETE -->
<#-- pref for project_scan_id  -->
<#assign pscanpref='0bbb0000-0000-4000-8000-**********0'>
<#assign pscanpref2='0bbb0000-0000-4000-8000-**********'>
<#-- pref for app_project_id  -->
<#assign appprojpref='aabb0000-0000-4000-8000-**********0'>
<#-- pref for project_id  -->
<#assign projpref='00bb0000-0000-4000-8000-**********0'>
<#-- pref for id in project_scan table -->
<#assign psidref='000b0000-0000-4000-8000-**********0'>
<#-- pref for id in project_scan table -->
<#assign psidref2='000b0000-0000-4000-8000-**********'>
<#-- DO NOT MODIFY DELETE END -->
<#-- adding scan findings -->
<#assign scanfref='scanfind-0000-4000-8000-**********0'>
<#-- adding finding -->
<#assign findingref='finding0-0000-4000-8000-**********0'>
<#-- adding scan config id-->
<#assign scanConfigId='scanconf-8a8a-8aaa-aa88-800a00000'>
<#-- adding scans for sca scan result service api tests -->
<#assign srScanId='cccc0000-0000-4000-8000-000000000'>
<#-- adding artifacts -->
<#assign artiref='artifact-0000-4000-8000-**********'>




<#-- insert into product table -->
<#list [1,2,3,4,5,6] as prodId>
    <#if prodId < 5>
        Insert into
        PRODUCT (PRODUCT_ID,PRODUCT_NAME,VERSION,LANGUAGE,LICENSE,ARTIFACT_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,UNIQUE_LOOKUP_VALUE)
        values ('${prodref}${prodId?string}','product-${1?c}','1.0.${prodId?string}','JAVA','Apache-2.0',
        '${artiref}${0+prodId?string}',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,'maven:product-${1?c}:1.0.${prodId?string}:');
    <#else>
        Insert into
        PRODUCT (PRODUCT_ID,PRODUCT_NAME,VERSION,LANGUAGE,LICENSE,ARTIFACT_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,UNIQUE_LOOKUP_VALUE)
        values ('${prodref}${prodId?string}','product-${2?c}','1.0.${prodId?string}','JAVASCRIPT','MIT',
        '${artiref}${0+prodId?string}',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,'npm:product-${2?c}:1.0.${prodId?string}:');
    </#if>
<#-- insert into product_license table -->
    <#if 4 < prodId>
        Insert into
        PRODUCT_LICENSE (PRODUCT_LICENSE_ID,PRODUCT_ID,SPDX_ID,MODIFIED_BY)
        values ('${prodlicref}${prodId?string}','${prodref}${prodId?string}','Apache-2.0','scanresult');
    </#if>
    <#if 4 == prodId>
        Insert into
        PRODUCT_LICENSE (PRODUCT_LICENSE_ID,PRODUCT_ID,SPDX_ID,MODIFIED_BY)
        values ('${prodlicref}${prodId?string}','${prodref}${prodId?string}','LGPL-2.0','scanresult');
    </#if>
    <#if prodId < 4>
        Insert into
        PRODUCT_LICENSE (PRODUCT_LICENSE_ID,PRODUCT_ID,SPDX_ID,MODIFIED_BY)
        values ('${prodlicref}${prodId?string}','${prodref}${prodId?string}','MPL-1.0','scanresult');

        Insert into
        PRODUCT_LICENSE (PRODUCT_LICENSE_ID,PRODUCT_ID,SPDX_ID,MODIFIED_BY)
        values ('${prodlicref}${(prodId + 6)?string}','${prodref}${prodId?string}','MIT','scanresult');
    </#if>
<#-- insert into component table -->
    <#if prodId < 5>
    Insert into
    COMPONENT (COMPONENT_ID, PRODUCT_ID, FILE_NAME, VERSION, MODIFIED_BY,UNIQUE_LOOKUP_VALUE)
    values ('${compref}${prodId?string}','${prodref}${prodId?string}','comp-file-name-${prodId?string}', '1.0.${prodId?string}', 'scanresult', 'maven:product-${1?c}:1.0.${prodId?string}:');
    <#else>
        Insert into
        COMPONENT (COMPONENT_ID, PRODUCT_ID, FILE_NAME, VERSION, MODIFIED_BY,UNIQUE_LOOKUP_VALUE)
        values ('${compref}${prodId?string}','${prodref}${prodId?string}','comp-file-name-${prodId?string}', '1.0.${prodId?string}', 'scanresult', 'npm:product-${2?c}:1.0.${prodId?string}:');
    </#if>
</#list>

<#-- insert into cve table -->
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1001','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-211',2.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2017-01-16 14:59:03.389','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:07.669','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:11.573','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:15.148','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:18.573','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','1',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',1.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',2.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',1.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1002','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-212',4.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2017-01-23 19:49:18.956','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:25.837','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:29.796','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:37.245','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:41.31','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','1',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',4.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',4.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',3.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1003','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-213',5.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2007-12-31 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('1999-12-12 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2008-09-09 04:36:37.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 05:12:20.43','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 10:08:46.044','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','4',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',4.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',5.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',5.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1004','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-215',1.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2017-01-23 19:49:18.956','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:25.837','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:29.796','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:37.245','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:41.31','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','1',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',2.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',1.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',6.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1005','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-215',4.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2017-01-23 19:49:18.956','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:25.837','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:29.796','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:37.245','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-23 19:49:41.31','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','1',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',4.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',4.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',10.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1006','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-216',3.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2007-12-31 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('1999-12-12 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2008-09-09 04:36:37.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 05:12:20.43','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 10:08:46.044','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','4',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',3.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',3.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',7.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1007','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-217',4.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2007-12-31 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('1999-12-12 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2008-09-09 04:36:37.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 05:12:20.43','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 10:08:46.044','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','4',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',3.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',4.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',5.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1008','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-218',5.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2007-12-31 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('1999-12-12 19:00:00.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2008-09-09 04:36:37.0','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 05:12:20.43','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-14 10:08:46.044','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','4',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',5.5,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',5.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',2.0,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');
Insert into CVE (cve_id,summary,cwe_id,score,attack_vector,access_complexity,authentication,confidentiality_impact,availability_impact,integrity_impact,source,generated_ts,published_ts,last_modified_ts,insert_ts,modified_ts,modified_by,record_ver,srcclr_id,vuln_title,suppressed,nvd_cvss_vector, nvd_cvss3_score, nvd_cvss3_vector,srcclr_cvss_score,srcclr_cvss_vector,srcclr_cvss3_score,srcclr_cvss3_vector) values ('CVE-0000-1009','War FTP Daemon 1.70 allows remote attackers to cause a denial of service by flooding it with connections.','CWE-219',2.0,'NETWORK','LOW','NONE','NONE','PARTIAL',null,'http://nvd.nist.gov',to_timestamp('2017-01-16 14:59:03.389','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:07.669','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:11.573','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:15.148','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 14:59:18.573','YYYY-MM-DD HH24:MI:SS:MS'),'INTERNAL','1',null,null,'false','AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',2.2,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P',2.0,'AV:N/​AC:L/​Au:N/​C:N/​I:N/​A:P',1.1,'AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:P');

<#-- insert into component_cve table -->
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-1','${compref}1','CVE-0000-1001',CURRENT_TIMESTAMP,true,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-2','${compref}1','CVE-0000-1002',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-3','${compref}2','CVE-0000-1001',CURRENT_TIMESTAMP,true,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-4','${compref}2','CVE-0000-1002',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-5','${compref}2','CVE-0000-1003',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-6','${compref}2','CVE-0000-1004',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-7','${compref}3','CVE-0000-1002',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-8','${compref}3','CVE-0000-1006',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-9','${compref}3','CVE-0000-1007',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-10','${compref}4','CVE-0000-1008',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);
Insert into COMPONENT_CVE (COMPONENT_CVE_ID,COMPONENT_ID,CVE_ID,INSERT_TS,SUPPRESSED,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('compcve-11','${compref}4','CVE-0000-1002',CURRENT_TIMESTAMP,false,CURRENT_TIMESTAMP,'scanresult',1);

<#-- insert into account_blacklist table -->
<#assign i=0>
<#list accountIds as accountId>
    <#assign i=accountId?index + accountId?index*2>
    INSERT INTO ACCOUNT_BLACKLIST(ACCOUNT_BLACKLIST_ID,ACCOUNT_ID,COMPONENT_ID,REMEDIATION_TEXT,BLACKLISTED,LAST_ACTIVE_TS,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    VALUES('acctblacklist-${i?string}',${accountId},'${compref}1','scadev-test',true,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'INTERNAL',0);
    INSERT INTO ACCOUNT_BLACKLIST(ACCOUNT_BLACKLIST_ID,ACCOUNT_ID,COMPONENT_ID,REMEDIATION_TEXT,BLACKLISTED,LAST_ACTIVE_TS,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    VALUES('acctblacklist-${(i+1)?string}',${accountId},'${compref}2','scadev-test',true,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'INTERNAL',0);
</#list>


<#-- insert into scan table -->
<#assign i=accountIds[0]>
<#list [1,2,3] as k>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER,START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${scanpref}${k?string}',${i},${k},1001,1001,${k},${k},true,false,'test/app1001','q',null,null,'COMPLETED','Completed',4,'Server1',to_timestamp('2017-01-0${(k+1)?string} 15.38.17.642','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-0${(k+1)?string} 15.38.22.059', 'YYYY-MM-DD HH24:MI:SS:MS'),null,false,to_timestamp('2017-01-16 15.38.33.196','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 15.38.36.603','YYYY-MM-DD HH24:MI:SS:MS'),'scanresult',1);
</#list>
<#assign i=accountIds[1]>
<#list [4,5,6] as k>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER,START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${scanpref}${k?string}',${i},${k},1002,1002,${k},${k},true,false,'test/app1002','q',null,null,'COMPLETED','Completed',4,'Server1',to_timestamp('2017-01-0${(k+1)?string} 15.38.17.642','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-0${(k+1)?string} 15.38.22.059','YYYY-MM-DD HH24:MI:SS:MS'),null,false,to_timestamp('2017-01-16 15.38.33.196','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 15.38.36.603','YYYY-MM-DD HH24:MI:SS:MS'),'scanresult',1);
</#list>
<#assign i=accountIds[1]>
<#list [7,8,9] as k>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER,START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${scanpref}${k?string}',${i},${k},2001,2001,${k},${k},true,false,'test/app1003','q',null,null,'COMPLETED','Completed',4,'Server1',to_timestamp('2017-01-0${(k+1)?string} 15.38.17.642','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-0${(k+1)?string} 15.38.17.642','YYYY-MM-DD HH24:MI:SS:MS'),null,false,to_timestamp('2017-01-16 15.38.33.196','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-16 15.38.36.603','YYYY-MM-DD HH24:MI:SS:MS'),'scanresult',1);
</#list>


<#-- insert into scanresult.scan table -->

<#assign i=accountIds[1]>
<#assign k='501'>
Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER, SCAN_CONFIG_ID, START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
values ('${srScanId}${k?string}',${i},${k},${k + 1},${k + 2},${k + 3},${k + 4},true,false,'${i}/${srScanId}${k?string}/${k + 1}/binaries','q','mock_key_440Byte','mock_key_256Byte','FAILED','FAILED',4,'mock_data_scan_result', '${scanConfigId}${k?string}', CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);

<#assign i=accountIds[1]>
<#list ['601','602'] as k>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER, SCAN_CONFIG_ID, START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${srScanId}${k?string}',${i},${k},${k + 1},${k + 2},${k + 3},${k + 4},true,true,'${i}/${srScanId}${k?string}/${k + 1}/binaries','q','mock_key_440Byte','mock_key_256Byte','INPROGRESS','INPROGRESS',4,'mock_data_scan_result','${scanConfigId}${k?string}',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
</#list>

<#assign i=accountIds[1]>
<#assign k='701'>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER,SCAN_CONFIG_ID,START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${srScanId}${k?string}',${i},${k},${k + 1},${k + 2},${k + 3},${k + 4},false,false,'${i}/${srScanId}${k?string}/${k + 1}/binaries','q','mock_key_440Byte','mock_key_256Byte','CREATED','CREATED',4,'mock_data_scan_result','${scanConfigId}${k?string}', CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,null,true,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);

<#assign i=accountIds[1]>
<#assign k='801'>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER, SCAN_CONFIG_ID, START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${srScanId}${k?string}',${i},${k},${k + 1},${k + 2},${k + 3},${k + 4},true,false,'${i}/${srScanId}${k?string}/${k + 1}/binaries','q','mock_key_440Byte','mock_key_256Byte','COMPLETED','COMPLETED',4,'mock_data_scan_result', '${scanConfigId}${k?string}', CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);

<#assign i=accountIds[1]>
<#assign k='901'>
    Insert into SCAN (SCAN_ID,ACCOUNT_ID,JOB_ID,APP_ID,SANDBOX_ID,APP_VER_ID,ANALYSIS_UNIT_ID,SCA_ENABLED,SANDBOX,BINARY_PATH,ALIAS,ENCRYPT_KEY,ENCRYPT_IV,STATUS,STATUS_INFO,num_components,SERVER, SCAN_CONFIG_ID, START_TS,COMPLETED_TS,UPDATED_TS,DELETED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER)
    values ('${srScanId}${k?string}',${i},${k},${k + 1},${k + 2},${k + 3},${k + 4},true,false,'${i}/${srScanId}${k?string}/${k + 1}/binaries','q','mock_key_440Byte','mock_key_256Byte','DELETED','DELETED',4,'mock_data_scan_result', '${scanConfigId}${k?string}', CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);


<#-- insert into scan_component table -->
<#assign m=1>
<#list [1,2,3,4,5,6,7,8,9] as scanId>
    <#if scanId < 5 >
        <#list ['1','2','3','4'] as componentId>
            <#assign d=m%27+1>
            Insert into SCAN_COMPONENT (SCAN_COMPONENT_ID,SCAN_ID,COMPONENT_ID,FILE_NAME,PRODUCT_ID,VERSION,LAST_SEEN_TS,INSERT_TS) values ('scancomponent-${m?string}','${scanpref}${scanId?string}','${compref}${componentId}','test-${m?string}','${prodref}${componentId}',1,to_timestamp('2017-01-${d?string} 15.39.18.478','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-${d?string} 15.39.22.246','YYYY-MM-DD HH24:MI:SS:MS'));
            <#assign m=m+1>
        </#list>
    <#else>
        <#list ['1','2','3','4'] as componentId>
            <#assign d=m%27+1>
            Insert into SCAN_COMPONENT (SCAN_COMPONENT_ID,SCAN_ID,COMPONENT_ID,FILE_NAME,PRODUCT_ID,VERSION,LAST_SEEN_TS,INSERT_TS) values ('scancomponent-${m?string}','${scanpref}${scanId?string}','${compref}${componentId}','test-${m?string}','${prodref}${componentId}',1,to_timestamp('2017-01-${d?string} 15.39.18.478','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-${d?string} 15.39.22.246','YYYY-MM-DD HH24:MI:SS:MS'));
            <#assign m=m+1>
        </#list>
    </#if>
</#list>


<#assign n=1>

<#-- insert into scan_component_path table -->
<#list 1..30 as id>
    <#assign p=n%3+1>
    Insert into SCAN_COMPONENT_PATH (SCAN_COMPONENT_PATH_ID,SCAN_COMPONENT_ID,FILE_PATH,INSERT_TS,FILE_NAME)
    values ('scancomponentpath-${n?string}','scancomponent-${n?string}','test/app100${p?string}',to_timestamp('2017-01-${n?string} 15.39.54.159','YYYY-MM-DD HH24:MI:SS:MS'),'test-${n?string}');
    <#assign n=n+1>
</#list>

<#-- insert into mitigation table -->
<#assign y=0>
<#assign x=1000>
<#list 1..8 as mitId>
    <#list 1..2 as compId>
        <#list 1..2 as cveId>
            <#if (y%16) == 0>
                <#assign x = x+1>
            </#if>
            <#assign q=y%31+1>
            Insert into MITIGATION (MITIGATION_ID,ACCOUNT_ID,APP_ID,COMPONENT_ID,CVE_ID,MITIGATION_STATUS,MITIGATION_REASON,MITIGATION_ACTION,COMMENTS,DELETED,USER_NAME,USER_LOGIN_ACCOUNT_ID,USER_ACCOUNT_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('mitigation-${(y+1)?string}',101,${x?c},'${compref}${compId}','CVE-0000-100${cveId?string}','PROPOSED','BYENV','BYENV','test',false,'scanresult',1,101,to_timestamp('2017-01-${(q)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-03-${q?string} 10.20.59.454','YYYY-MM-DD HH24:MI:SS:MS'),'scanresult',1);
            <#assign y = y+1>
        </#list>
    </#list>
</#list>

<#-- insert into finding table -->
<#assign x = 0>
<#list 1..6 as findingId>
    <#assign x = x+1>
    Insert into finding (FINDING_ID,APP_ID,COMPONENT_ID,PRODUCT_ID,FILE_PATH,CVE_ID,INSERT_TS,SUPPRESSED)
    values('${findingref}${findingId?string}',${100+x?c},'${compref}${findingId?string}','${prodref}${findingId?string}','comp-file-name-${findingId?string}','CVE-0000-100${x?string}',CURRENT_TIMESTAMP,false);
</#list>

<#-- insert into scan_finding table -->
<#assign m=1>
<#list 1..2 as scanId>
    <#list ['1','2','3','4'] as componentId>
        <#assign d=m%27+1>
        Insert into scan_finding (SCAN_FINDING_ID,SCAN_ID,FINDING_ID,COMPONENT_ID,PRODUCT_ID,FILE_PATH,CVE_ID,INSERT_TS,SUPPRESSED)
        values('${scanfref}${m?string}','${scanpref}${scanId?string}','${findingref}${componentId?string}','${compref}${componentId?string}','${prodref}${componentId?string}','test/app100${m?string}','CVE-0000-100${m?string}',to_timestamp('2017-01-${d?string} 15.39.18.478','YYYY-MM-DD HH24:MI:SS:MS'),false);
        <#assign m=m+1>
    </#list>
</#list>


<#-- insert into artifact and artifact_file_path table -->
<#assign m=1>
<#assign artifactId = 1>
        <#list 1..5 as version>
            Insert into artifact (ARTIFACT_ID,ARTIFACT_SOURCE,ARTIFACT_NAME,NORMALIZED_VERSION,GROUP_ID,MODULE_ID,VERSION,LICENSE_URL,LICENSE,PROJECT_URL,MODULE_HASH,MODULE_NAME,ORG_NAME,DESCRIPTION,LAST_MODIFIED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,LATEST_VERSION,KEYWORDS,GROUP_ALIAS,INTEGRITY,RESOLUTION,UNIQUE_LOOKUP_VALUE)
            values ('${artiref}${0+version?string}','MAVEN','group_id1:module_id1','1.0.${version?string}','group_id1','module_id1','1.0.${version?string}',null,'Apache-2.0',null,null,null,null,null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,false,null,null,null,null,'maven:group_id1:module_id1:1.0.${version?string}:');

            Insert into artifact_file_path(ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,FILE_NAME,SHA1,FILE_NAME_LOWER,INSERT_TS)
            values ('${artiref}${0+version?string}','maven:group_id1:module_id1:1.0.${version?string}:','file_name-${version?string}','sha1-${version?string}','file_name-${version?string}',CURRENT_TIMESTAMP);
            <#assign artifactId += 1>
        </#list>
        <#list 1..4 as version>
            Insert into artifact (ARTIFACT_ID,ARTIFACT_SOURCE,ARTIFACT_NAME,NORMALIZED_VERSION,GROUP_ID,MODULE_ID,VERSION,LICENSE_URL,LICENSE,PROJECT_URL,MODULE_HASH,MODULE_NAME,ORG_NAME,DESCRIPTION,LAST_MODIFIED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,LATEST_VERSION,KEYWORDS,GROUP_ALIAS,INTEGRITY,RESOLUTION,UNIQUE_LOOKUP_VALUE)
            values ('${artiref}${0+(version+5)?string}','NPM','npm:module_id2','1.0.${version?string}','group_id2','module_id2','1.0.${version?string}',null,'MIT',null,null,null,null,null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,false,null,null,null,null,'npm:module_id2::1.0.${version?string}:');

            Insert into artifact_file_path(ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,FILE_NAME,SHA1,FILE_NAME_LOWER,INSERT_TS)
            values('${artiref}${0+(version+5)?string}','maven:group_id1:module_id1:1.0.${(version+5)?string}:','file_name-${(version+5)?string}','sha1-${(version+5)?string}','file_name-${(version+5)?string}',CURRENT_TIMESTAMP);
            <#assign artifactId += 1>
        </#list>
        <#list 1..5 as version>
            Insert into artifact (ARTIFACT_ID,ARTIFACT_SOURCE,ARTIFACT_NAME,NORMALIZED_VERSION,GROUP_ID,MODULE_ID,VERSION,LICENSE_URL,LICENSE,PROJECT_URL,MODULE_HASH,MODULE_NAME,ORG_NAME,DESCRIPTION,LAST_MODIFIED,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,LATEST_VERSION,KEYWORDS,GROUP_ALIAS,INTEGRITY,RESOLUTION,UNIQUE_LOOKUP_VALUE)
            values ('${artiref}${(version+9)?string}','BOWER','bower:module_id3','1.0.${version?string}','group_id3','module_id3','1.0.${version?string}',null,'Apache-2.0',null,null,null,null,null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,false,null,null,null,null,'bower:module_id3::1.0.${version?string}:');

            Insert into artifact_file_path(ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,FILE_NAME,SHA1,FILE_NAME_LOWER,INSERT_TS)
            values('${artiref}${(version+9)?string}','maven:group_id1:module_id1:1.0.${(version+9)?string}:','file_name-${(version+9)?string}','sha1-${(version+9)?string}','file_name-${(version+9)?string}',CURRENT_TIMESTAMP);
            <#assign artifactId += 1>
        </#list>

<#-- insert into artifact_cve table -->
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,SUPPRESSED) values ('artifact-0000-0000-0000-**********01','0000','CVE-0000-1001',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,true);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********01','0000','CVE-0000-1002',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER,SUPPRESSED) values ('artifact-0000-0000-0000-**********03','0003','CVE-0000-1001',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1,true);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********04','0004','CVE-0000-1002',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********04','0004','CVE-0000-1003',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********04','0004','CVE-0000-1004',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********05','0005','CVE-0000-1002',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********06','0006','CVE-0000-1006',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********07','0007','CVE-0000-1007',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********08','0008','CVE-0000-1008',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);
Insert into ARTIFACT_CVE (ARTIFACT_ID,UNIQUE_LOOKUP_VALUE,CVE_ID,DISCOVERY_ID,INSERT_TS,MODIFIED_TS,MODIFIED_BY,RECORD_VER) values ('artifact-0000-0000-0000-**********09','0009','CVE-0000-1002',null,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'scanresult',1);

<#--- DEV TESTING DATA DO NOT MODIFY or REMOVE---->
<#-- mock data used by dev testing new queries on new tables and views -->
<#-- insert into project_scan_component table -->
<#assign p=1>
<#assign psc=1>
<#assign psid=1>
<#list [1,2] as j>
<#list 1..14 as k>
        <#assign d=p%9+1>
        <#assign scanId =1>
        <#list ['1','2','3','4'] as componentId>
            <#if (psid < 10)>
                Insert into PROJECT_SCAN_COMPONENT (PROJECT_SCAN_COMPONENT_ID,SCAN_ID,PROJECT_SCAN_ID,COMPONENT_ID,FILE_NAME,PRODUCT_ID,VERSION,LAST_SEEN_TS,INSERT_TS)
                values ('pscancomponent-${psc?string}','${scanpref}${j?string}','${pscanpref}${psid?string}','${compref}${componentId}','test-${componentId?string}','${prodref}${componentId}',1,to_timestamp('2017-01-${0?string}${d?string} 15.39.18.478','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-${0?string}${d?string} 15.39.22.246','YYYY-MM-DD HH24:MI:SS:MS'));
                <#assign psc=psc+1>
            <#else>
                Insert into PROJECT_SCAN_COMPONENT (PROJECT_SCAN_COMPONENT_ID,SCAN_ID,PROJECT_SCAN_ID,COMPONENT_ID,FILE_NAME,PRODUCT_ID,VERSION,LAST_SEEN_TS,INSERT_TS)
                values ('pscancomponent-${psc?string}','${scanpref}${j?string}','${pscanpref2}${psid?string}','${compref}${componentId}','test-${componentId?string}','${prodref}${componentId}',1,to_timestamp('2017-01-${0?string}${d?string} 15.39.18.478','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2017-01-${0?string}${d?string} 15.39.22.246','YYYY-MM-DD HH24:MI:SS:MS'));
                <#assign psc=psc+1>
            </#if>
        </#list>
        <#assign psid=psid+1>
        <#assign p=p+1>
</#list>
</#list>

<#-- insert into app_project table -->
<#list 1..4 as v>
INSERT INTO app_project(app_project_id, app_id, project_id, project_name, workspace_name,default_branch, linked, linked_ts, unlinked_ts, deleted, delete_ts,insert_ts, modified_ts)
VALUES ('${appprojpref}${(v)?string}',1001, '${projpref}${(v)?string}', 'test project ${(v)?string}', 'work space 1','branch aaa', true, to_timestamp('2019-05-${(1)?string}${(v)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),null,false, null,to_timestamp('2019-05-${(1)?string}${(v)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);
</#list>
<#list 5..7 as v>
    INSERT INTO app_project(app_project_id, app_id, project_id, project_name, workspace_name,default_branch, linked, linked_ts, unlinked_ts, deleted, delete_ts,insert_ts, modified_ts)
    VALUES ('${appprojpref}${(v)?string}',1002, '${projpref}${(v)?string}', 'test project ${(v)?string}', 'work space 1','branch aaa', true, to_timestamp('2019-05-${(1)?string}${(v)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),null,false, null,to_timestamp('2019-05-${(1)?string}${(v)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);
</#list>

<#-- insert into project_scan table -->
<#assign ps = 1>

<#list 1..2 as scan>
    <#list 1..2 as p>
<#list 1..7 as w>
    <#if (ps < 10)>
        <#if (ps%7 != 0)>
            INSERT INTO project_scan(id, project_id, project_scan_id, scan_id, latest, scan_ts, insert_ts,modified_ts)
            VALUES ('${psidref}${ps?string}','${projpref}${p?string}', '${pscanpref}${ps?string}','${scanpref}${scan?string}', false,to_timestamp('2019-05-${(1)?string}${(w)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2019-05-${(1)?string}${(w)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);
            <#assign ps += 1>
        <#else>
            INSERT INTO project_scan(id, project_id, project_scan_id, scan_id, latest, scan_ts, insert_ts,modified_ts)
            VALUES ('${psidref}${ps?string}','${projpref}${p?string}', '${pscanpref}${ps?string}','${scanpref}${scan?string}', true,to_timestamp('2019-05-${(1)?string}${w?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2019-05-${(1)?string}${w?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);
            <#assign ps += 1>
        </#if>
    <#else>
        <#if (ps%7 != 0)>
        INSERT INTO project_scan(id, project_id, project_scan_id, scan_id, latest, scan_ts, insert_ts,modified_ts)
        VALUES ('${psidref2}${ps?string}','${projpref}${p?string}', '${pscanpref2}${ps?string}','${scanpref}${scan?string}', false,to_timestamp('2019-05-${(1)?string}${(w)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2019-05-${(1)?string}${(w)?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);
        <#assign ps += 1>
        <#else>
            INSERT INTO project_scan(id, project_id, project_scan_id, scan_id, latest, scan_ts, insert_ts,modified_ts)
            VALUES ('${psidref2}${ps?string}','${projpref}${p?string}', '${pscanpref2}${ps?string}','${scanpref}${scan?string}', true,to_timestamp('2019-05-${(1)?string}${w?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2019-05-${(1)?string}${w?string} 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);
            <#assign ps += 1>
        </#if>
    </#if>
</#list>
</#list>
</#list>
<#----insert few edge cases for dev query and view testing DO  -->
INSERT INTO project_scan(id, project_id, project_scan_id, scan_id, latest, scan_ts, insert_ts,modified_ts)
VALUES ('000b0000-0000-4000-8000-**********29','00bb0000-0000-4000-8000-**********01',
'0bbb0000-0000-4000-8000-**********29','bbbb0000-0000-4000-8000-**********03',
true,to_timestamp('2019-05-15 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),
to_timestamp('2019-05-15 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);

INSERT INTO project_scan(id, project_id, project_scan_id, scan_id, latest, scan_ts, insert_ts,modified_ts)
VALUES ('000b0000-0000-4000-8000-**********30','00bb0000-0000-4000-8000-**********01',
'0bbb0000-0000-4000-8000-**********29','bbbb0000-0000-4000-8000-**********04',
true,to_timestamp('2019-05-15 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'),
to_timestamp('2019-05-15 10.20.55.67','YYYY-MM-DD HH24:MI:SS:MS'), CURRENT_TIMESTAMP);

INSERT INTO PROJECT_SCAN_COMPONENT (PROJECT_SCAN_COMPONENT_ID,SCAN_ID,PROJECT_SCAN_ID,COMPONENT_ID,FILE_NAME,PRODUCT_ID,VERSION,LAST_SEEN_TS,INSERT_TS)
values ('pscancomponent-113','bbbb0000-0000-4000-8000-**********03','0bbb0000-0000-4000-8000-**********29',
'cccc0000-0000-4000-8000-**********01','test-1','product0-0000-4000-8000-**********01',1,
to_timestamp('2019-05-16 15.39.18.478','YYYY-MM-DD HH24:MI:SS:MS'),to_timestamp('2019-05-16 15.39.22.246','YYYY-MM-DD HH24:MI:SS:MS'));

<#--- END OF DEV TESTING DATA ---->

