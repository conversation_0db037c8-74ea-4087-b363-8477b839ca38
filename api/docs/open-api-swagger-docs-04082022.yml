{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://localhost:8509/sca/scanresult", "description": "Generated server url"}], "paths": {"/v1/scans/{scan_id}/status": {"put": {"tags": ["scan-controller"], "operationId": "updateScanStatus", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanStatusUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/{scan_id}/status": {"put": {"tags": ["scan-controller"], "operationId": "updateScanStatus_1", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanStatusUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans/{scan_id}/promote": {"put": {"tags": ["scan-controller"], "operationId": "promoteScan", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanPromoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/{scan_id}/promote": {"put": {"tags": ["scan-controller"], "operationId": "promoteScan_1", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanPromoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans/{scan_id}": {"get": {"tags": ["scan-controller"], "operationId": "getScan", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["scan-controller"], "operationId": "updateScan", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/{scan_id}": {"get": {"tags": ["scan-controller"], "operationId": "getScan_1", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["scan-controller"], "operationId": "updateScan_1", "parameters": [{"name": "scan_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans/deleteScans": {"put": {"tags": ["scan-controller"], "operationId": "deleteScans", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppScanUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/deleteScans": {"put": {"tags": ["scan-controller"], "operationId": "deleteScans_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppScanUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/{application}/alerts/status": {"put": {"tags": ["application-controller"], "operationId": "updateApplicationAlertStatus", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string", "format": "date-time"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/alerts/status": {"put": {"tags": ["application-controller"], "operationId": "updateApplicationAlertStatus_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string", "format": "date-time"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:promote": {"put": {"tags": ["analysis-unit-controller"], "operationId": "promoteScan_2", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanPromoteRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/vulnerabilities": {"post": {"tags": ["portfolio-controller-v-2"], "operationId": "getVulnerabilities", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelVulnerabilityPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/components": {"post": {"tags": ["portfolio-controller-v-2"], "operationId": "getComponents", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerComponentPortfolioResult_Portfolio"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelComponentPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/blacklist/result": {"post": {"tags": ["blacklist-controller-v-2"], "operationId": "getBlacklist", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAccountBlacklistSummary"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlacklistRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAccountBlacklistSummary"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/blocklist/result": {"post": {"tags": ["blacklist-controller-v-2"], "operationId": "getBlacklist_1", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAccountBlacklistSummary"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlacklistRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAccountBlacklistSummary"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/blacklist": {"get": {"tags": ["blacklist-controller-v-2"], "operationId": "getBlacklistV2", "parameters": [{"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerBlocklistDetail"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelBlocklistDetailResource"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "post": {"tags": ["blacklist-controller-v-2"], "operationId": "createBlacklist", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/blocklist": {"get": {"tags": ["blacklist-controller-v-2"], "operationId": "getBlacklistV2_1", "parameters": [{"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerBlocklistDetail"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelBlocklistDetailResource"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "post": {"tags": ["blacklist-controller-v-2"], "operationId": "createBlacklist_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/applications": {"post": {"tags": ["portfolio-controller-v-2"], "operationId": "getApplications", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerLinkedAppPortfolioResult_Portfolio"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelLinkedAppPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/applications/result": {"post": {"tags": ["portfolio-controller-v-2"], "operationId": "getResults", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerApplicationResult"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelApplicationResult"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/vulnerabilities": {"post": {"tags": ["portfolio-controller"], "operationId": "getVulnerabilities_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelVulnerabilityPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/vulnerabilities": {"post": {"tags": ["portfolio-controller"], "operationId": "getVulnerabilities_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelVulnerabilityPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/scans/{scan}/shares": {"post": {"tags": ["share-scan-controller"], "operationId": "shareScan", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareScanKey"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareScan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["share-scan-controller"], "operationId": "unshareScan", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "app_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "account_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/{scan}/shares": {"post": {"tags": ["share-scan-controller"], "operationId": "shareScan_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareScanKey"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareScan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["share-scan-controller"], "operationId": "unshareScan_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "app_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "account_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans": {"post": {"tags": ["scan-controller"], "operationId": "createScan", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans": {"post": {"tags": ["scan-controller"], "operationId": "createScan_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/projects": {"post": {"tags": ["project-controller"], "operationId": "updateProjects", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteProjects"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/projects": {"post": {"tags": ["project-controller"], "operationId": "updateProjects_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteProjects"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/components/{component}/otherversions": {"post": {"tags": ["component-controller"], "operationId": "getComponentOtherVersionsReport", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAppRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelArtifactFilePortfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/components/{component}/otherversions": {"post": {"tags": ["component-controller"], "operationId": "getComponentOtherVersionsReport_1", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAppRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelArtifactFilePortfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/components/{component}/applications": {"post": {"tags": ["component-controller"], "operationId": "getDependentApplications", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAppRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelLinkedAppPortfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/components/{component}/applications": {"post": {"tags": ["component-controller"], "operationId": "getDependentApplications_1", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComponentAppRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelLinkedAppPortfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/components": {"post": {"tags": ["portfolio-controller"], "operationId": "getComponents_1", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerComponentPortfolioResult_Portfolio"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelComponentPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/components": {"post": {"tags": ["portfolio-controller"], "operationId": "getComponents_2", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerComponentPortfolioResult_Portfolio"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelComponentPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/blacklist/result": {"post": {"tags": ["blacklist-controller"], "operationId": "getBlacklist_2", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAccountBlacklistSummary"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlacklistRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAccountBlacklistSummary"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/blacklist/result": {"post": {"tags": ["blacklist-controller"], "operationId": "getBlacklist_3", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAccountBlacklistSummary"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlacklistRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAccountBlacklistSummary"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/blacklist": {"post": {"tags": ["blacklist-controller"], "operationId": "createBlacklist_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/blacklist": {"post": {"tags": ["blacklist-controller"], "operationId": "createBlacklist_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountBlacklist"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/applications/{application}/mitigations": {"get": {"tags": ["application-controller"], "operationId": "getMitigations", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cwe_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "mitigation_reason", "in": "query", "required": false, "schema": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}}, {"name": "mitigationType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["VULNERABILITY", "LICENSE"]}}, {"name": "mitigationStatus", "in": "query", "required": false, "schema": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelAppMitigation"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "post": {"tags": ["application-controller"], "operationId": "createMitigations", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkMitigation"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkMitigationResult"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/mitigations": {"get": {"tags": ["application-controller"], "operationId": "getMitigations_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cwe_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "mitigation_reason", "in": "query", "required": false, "schema": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}}, {"name": "mitigationType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["VULNERABILITY", "LICENSE"]}}, {"name": "mitigationStatus", "in": "query", "required": false, "schema": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelAppMitigation"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "post": {"tags": ["application-controller"], "operationId": "createMitigations_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkMitigation"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkMitigationResult"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/{application}/linkedproject": {"post": {"tags": ["application-controller"], "operationId": "createAppProject", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppProject"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppProject"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/linkedproject": {"post": {"tags": ["application-controller"], "operationId": "createAppProject_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppProject"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppProject"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/result": {"post": {"tags": ["portfolio-controller"], "operationId": "getResults_1", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerApplicationResult"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelApplicationResult"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/applications/result": {"post": {"tags": ["portfolio-controller"], "operationId": "getResults_2", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerApplicationResult"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelApplicationResult"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/applications": {"post": {"tags": ["portfolio-controller"], "operationId": "getApplications_1", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAppPortfolioResult_Portfolio"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAppPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/applications": {"post": {"tags": ["portfolio-controller"], "operationId": "getApplications_2", "parameters": [{"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAppPortfolioResult_Portfolio"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortfolioRequest_Portfolio"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAppPortfolioResult_Portfolio"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/analysis_units/{analysis_unit_id}/scan:share": {"post": {"tags": ["analysis-unit-controller"], "operationId": "shareScan_2", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareScanKey"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShareScan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["analysis-unit-controller"], "operationId": "unshareScan_2", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "app_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "account_id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/integration/applications/{application}/findings": {"post": {"tags": ["finding-controller-v-2"], "operationId": "getScanFindings", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "sandbox_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "severity", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "severity_gte", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cwe_id", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvss", "in": "query", "required": false, "schema": {"type": "number", "format": "float"}}, {"name": "cvss_gte", "in": "query", "required": false, "schema": {"type": "number", "format": "float"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "dependency_mode", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sca_scan_mode", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerAppScanFindingWrapper"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanPolicy"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelAppScanFindingWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getScanFromAuId", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["analysis-unit-controller"], "operationId": "deleteScanByAuId", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "patch": {"tags": ["analysis-unit-controller"], "operationId": "updateScanByAuId", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanUpdate"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scan"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/vulnerabilities": {"get": {"tags": ["scan-controller-v-2"], "operationId": "getVulnerabilities_3", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "has_license", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "has_risk_rating", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelLinkedCommonScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/result": {"get": {"tags": ["scan-controller-v-2"], "operationId": "getResults_3", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "prev_scan", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy_ComponentDetail"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedScanResult_ComponentDetail"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/policycompliance": {"get": {"tags": ["scan-controller-v-2"], "operationId": "getPolicycompliance", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "app_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}, {"name": "policy", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PolicyCompliance"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/licenses": {"get": {"tags": ["scan-controller-v-2"], "operationId": "getLicenses", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanLicenseModel"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/components": {"get": {"tags": ["scan-controller-v-2"], "operationId": "getComponents_3", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "has_license", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy_ComponentSummary"}}, {"name": "has_risk_rating", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelEntityModelScanComponentModel_ComponentSummary"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/components/{component}": {"get": {"tags": ["scan-component-controller-v-2"], "operationId": "getComponentDetail", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanComponentModel"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/{scan}/components/{component}/vulnerabilities": {"get": {"tags": ["scan-component-controller-v-2"], "operationId": "getScanComponentVulns", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelLinkedScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v2/scans/lastcompletedscan": {"get": {"tags": ["scan-controller-v-2"], "operationId": "getLatestCompletedScan", "parameters": [{"name": "fields", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/workspaces/{workspace_id}/issues": {"get": {"tags": ["workspace-controller"], "operationId": "getIssues", "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/workspaces/{workspace_id}/issues": {"get": {"tags": ["workspace-controller"], "operationId": "getIssues_1", "parameters": [{"name": "workspace_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans/{scan}/vulnerabilities": {"get": {"tags": ["scan-controller"], "operationId": "getVulnerabilities_4", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "has_license", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "has_risk_rating", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/scans/{scan}/vulnerabilities": {"get": {"tags": ["scan-controller"], "operationId": "getVulnerabilities_5", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "has_license", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "has_risk_rating", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/scans/{scan}/result": {"get": {"tags": ["scan-controller"], "operationId": "getResults_4", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "prev_scan", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy_ComponentDetail"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanResult_ComponentDetail"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/scans/{scan}/result": {"get": {"tags": ["scan-controller"], "operationId": "getResults_5", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "prev_scan", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy_ComponentDetail"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanResult_ComponentDetail"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/scans/{scan}/policycompliance": {"get": {"tags": ["scan-controller"], "operationId": "getPolicycompliance_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "app_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "policy", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PolicyCompliance"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/scans/{scan}/policycompliance": {"get": {"tags": ["scan-controller"], "operationId": "getPolicycompliance_2", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "app_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "policy", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PolicyCompliance"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/scans/{scan}/components/{component}/vulnerabilities": {"get": {"tags": ["scan-component-controller"], "operationId": "getScanComponentVulns_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/scans/{scan}/components/{component}/vulnerabilities": {"get": {"tags": ["scan-component-controller"], "operationId": "getScanComponentVulns_2", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/scans/{scan}/components/{component}/sources": {"get": {"tags": ["scan-component-controller"], "operationId": "getComponentSources", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/ComponentSourceWrapper"}, "key": {"type": "string"}}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/{scan}/components/{component}/sources": {"get": {"tags": ["scan-component-controller"], "operationId": "getComponentSources_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/ComponentSourceWrapper"}, "key": {"type": "string"}}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans/{scan}/components/{component}/mitigations": {"get": {"tags": ["scan-component-controller"], "operationId": "getMitigationHistory", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "generated_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelMitigation"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/scans/{scan}/components/{component}/mitigations": {"get": {"tags": ["scan-component-controller"], "operationId": "getMitigationHistory_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "generated_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelMitigation"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/scans/{scan}/components/{component}": {"get": {"tags": ["scan-component-controller"], "operationId": "getComponentDetail_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanComponent"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/scans/{scan}/components/{component}": {"get": {"tags": ["scan-component-controller"], "operationId": "getComponentDetail_2", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanComponent"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/orgs/{org_id}/workspaces": {"get": {"tags": ["workspace-controller"], "operationId": "getWorkspaces", "parameters": [{"name": "org_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workspace"}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/orgs/{org_id}/workspaces": {"get": {"tags": ["workspace-controller"], "operationId": "getWorkspaces_1", "parameters": [{"name": "org_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workspace"}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/components/{component}/vulnerabilities": {"get": {"tags": ["component-controller"], "operationId": "getComponentVulnerabilitiesReport", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelComponentCve"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/components/{component}/vulnerabilities": {"get": {"tags": ["component-controller"], "operationId": "getComponentVulnerabilitiesReport_1", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelComponentCve"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/components/{component}": {"get": {"tags": ["component-controller"], "operationId": "getComponentDetail_3", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityModelComponent"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/components/{component}": {"get": {"tags": ["component-controller"], "operationId": "getComponentDetail_4", "parameters": [{"name": "component", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityModelComponent"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/{application}/projects": {"get": {"tags": ["application-controller"], "operationId": "getApplicationLinkedProjects", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelEntityModelLinkedProject"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/projects": {"get": {"tags": ["application-controller"], "operationId": "getApplicationLinkedProjects_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelEntityModelLinkedProject"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/{application}/latestscan": {"get": {"tags": ["application-controller"], "operationId": "getLatestScan", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LatestScanDetails"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/latestscan": {"get": {"tags": ["application-controller"], "operationId": "getLatestScan_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LatestScanDetails"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/{application}/alerts": {"get": {"tags": ["application-controller"], "operationId": "getAppNotifications", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "alert_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelAlert"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/alerts": {"get": {"tags": ["application-controller"], "operationId": "getAppNotifications_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "alert_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelAlert"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/applications/alerted": {"get": {"tags": ["app-alert-controller"], "operationId": "getAlertedApplications", "parameters": [{"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "alert_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer"}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/alerted": {"get": {"tags": ["app-alert-controller"], "operationId": "getAlertedApplications_1", "parameters": [{"name": "batch_size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "alert_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer"}}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:vulnerabilities": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getVulnerabilities_6", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "has_license", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "has_risk_rating", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelLinkedCommonScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:result": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getResults_6", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "prev_analysis_unit_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "prev_scan", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy_ComponentDetail"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkedScanResult_ComponentDetail"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:policycompliance": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getPolicycompliance_3", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "app_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}, {"name": "policy", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PolicyCompliance"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:licenses": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getLicenses_1", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanLicenseModel"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:components": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getComponents_4", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "file_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "cve_id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "severities", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}}, {"name": "license_name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "has_license", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy_ComponentSummary"}}, {"name": "has_risk_rating", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelEntityModelScanComponentModel_ComponentSummary"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:components/{component_id}": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getComponentDetail_5", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "component_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScanComponentModel"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:components/{component_id}/vulnerabilities": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getVulnerabilitiesOfComponent", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "component_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "policy", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ScanPolicy"}}, {"name": "cvssType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelLinkedScanVulnerability"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/v1/analysis_units/{analysis_unit_id}/scan:components/{component_id}/mitigations": {"get": {"tags": ["analysis-unit-controller"], "operationId": "getMitigationHistoryOfComponent", "parameters": [{"name": "analysis_unit_id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "component_id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "generated_ts", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelMitigation"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/internal/v1/scans/{scan}/findings": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScanFindings_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerScanFindingWrapper"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelScanFindingWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/scans/{scan}/findings": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScanFindings_2", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerScanFindingWrapper"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelScanFindingWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/v1/scans/{scan}/components": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScanComponents", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "blacklist", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanComponent"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/scans/{scan}/components": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScanComponents_1", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "account_id", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "blacklist", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "risk_ratings", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollectionModelScanComponent"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/v1/scans/{scan}": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScan_2", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityModelScanWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/scans/{scan}": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScan_3", "parameters": [{"name": "scan", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EntityModelScanWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/v1/applications/{application}/scans": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScans", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerScanWrapper"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelScanWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/internal/applications/{application}/scans": {"get": {"tags": ["internal-integration-controller"], "operationId": "getScans_1", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "pageable", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/Pageable"}}, {"name": "assembler", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PagedResourcesAssemblerScanWrapper"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedModelEntityModelScanWrapper"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}, "deprecated": true}}, "/v1/applications/{application}/scans": {"delete": {"tags": ["application-controller"], "operationId": "deleteScans_2", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/applications/{application}/scans": {"delete": {"tags": ["application-controller"], "operationId": "deleteScans_3", "parameters": [{"name": "application", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}}, "components": {"schemas": {"ScanStatusUpdate": {"type": "object", "properties": {"scan_id": {"type": "string"}, "scan_config_id": {"type": "string"}, "status": {"type": "string", "enum": ["READY", "INITIALIZED", "COMPLETED", "FAILED", "INPROGRESS", "BINARYREADY"]}, "status_info": {"type": "string"}}}, "Scan": {"required": ["analysis_unit_id"], "type": "object", "properties": {"scan_id": {"type": "string"}, "scan_config_id": {"type": "string"}, "job_id": {"type": "integer", "format": "int64"}, "analysis_unit_id": {"type": "integer", "format": "int64"}, "sandbox": {"type": "boolean"}, "status": {"type": "string", "enum": ["READY", "INITIALIZED", "COMPLETED", "FAILED", "INPROGRESS", "BINARYREADY"]}, "status_info": {"type": "string"}, "num_components": {"type": "integer", "format": "int32"}, "completed_ts": {"type": "string", "format": "date-time"}, "deleted": {"type": "boolean"}}}, "ScanPromoteRequest": {"required": ["analysis_unit_id", "app_ver_id", "sandbox_id"], "type": "object", "properties": {"sandbox_id": {"type": "integer", "format": "int64"}, "app_ver_id": {"type": "integer", "format": "int64"}, "analysis_unit_id": {"type": "integer", "format": "int64"}}}, "ScanUpdate": {"type": "object", "properties": {"sandbox_id": {"type": "integer", "format": "int64"}, "app_ver_id": {"type": "integer", "format": "int64"}, "analysis_unit_id": {"type": "integer", "format": "int64"}, "sandbox": {"type": "boolean"}, "deleted": {"type": "boolean"}, "status": {"type": "string", "enum": ["READY", "INITIALIZED", "COMPLETED", "FAILED", "INPROGRESS", "BINARYREADY"]}}}, "AppScanUpdate": {"type": "object", "properties": {"app_id": {"type": "integer", "format": "int64"}, "delete": {"type": "boolean"}}}, "PortfolioRequest_Portfolio": {"type": "object", "properties": {"app_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "cve_id": {"type": "string"}, "license_name": {"type": "string"}, "has_license": {"type": "boolean"}, "file_name": {"type": "string"}, "blacklist": {"type": "boolean"}, "severities": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}, "risk_ratings": {"type": "array", "items": {"type": "string"}}, "has_license_risk_rating": {"type": "boolean"}, "sort": {"type": "string"}, "size": {"maximum": 2000, "type": "integer", "format": "int32"}, "page": {"maximum": 2000, "type": "integer", "format": "int32"}, "cvss_version": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}}, "CollectionModelVulnerabilityPortfolioResult_Portfolio": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/VulnerabilityPortfolioResult_Portfolio"}}}}, "Link_Portfolio": {"type": "object", "properties": {"rel": {"type": "string"}, "href": {"type": "string"}, "hreflang": {"type": "string"}, "media": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "deprecation": {"type": "string"}, "profile": {"type": "string"}, "name": {"type": "string"}}}, "VulnerabilityPortfolioResult_Portfolio": {"type": "object", "properties": {"cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "vuln_title": {"type": "string"}, "cwe_id": {"type": "string"}, "cve_description": {"type": "string"}, "component_count": {"type": "integer", "format": "int64"}, "cve_id": {"type": "string"}}}, "PagedResourcesAssemblerComponentPortfolioResult_Portfolio": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "EntityModelComponentPortfolioResult_Portfolio": {"type": "object", "properties": {"component_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "app_count": {"type": "integer", "format": "int64"}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "licenses": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/SpdxLicense_Portfolio"}}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_Portfolio"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}}}, "PageMetadata_Portfolio": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int64"}, "number": {"type": "integer", "format": "int64"}}}, "PagedModelEntityModelComponentPortfolioResult_Portfolio": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelComponentPortfolioResult_Portfolio"}}, "page": {"$ref": "#/components/schemas/PageMetadata_Portfolio"}}}, "SpdxLicense_Portfolio": {"type": "object", "properties": {"spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}}}, "VulnerabilitySummary_Portfolio": {"type": "object", "properties": {"veryHigh": {"type": "integer", "format": "int32"}, "high": {"type": "integer", "format": "int32"}, "medium": {"type": "integer", "format": "int32"}, "low": {"type": "integer", "format": "int32"}, "veryLow": {"type": "integer", "format": "int32"}, "informational": {"type": "integer", "format": "int32"}, "veryHighMitigated": {"type": "integer", "format": "int32"}, "highMitigated": {"type": "integer", "format": "int32"}, "mediumMitigated": {"type": "integer", "format": "int32"}, "lowMitigated": {"type": "integer", "format": "int32"}, "veryLowMitigated": {"type": "integer", "format": "int32"}, "informationalMitigated": {"type": "integer", "format": "int32"}, "highestScore": {"type": "number", "format": "float"}}}, "BlacklistRequest": {"type": "object", "properties": {"app_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "size": {"maximum": 2000, "type": "integer", "format": "int32"}, "page": {"maximum": 2000, "type": "integer", "format": "int32"}, "cvss_version": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}}, "PagedResourcesAssemblerAccountBlacklistSummary": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "EntityModelAccountBlacklistSummary": {"required": ["file_name", "version"], "type": "object", "properties": {"file_name": {"type": "string"}, "version": {"type": "string"}, "app_count": {"type": "integer", "format": "int32"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary"}, "component_id": {"type": "string"}, "account_id": {"type": "integer", "format": "int64"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "Link": {"type": "object", "properties": {"rel": {"type": "string"}, "href": {"type": "string"}, "hreflang": {"type": "string"}, "media": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "deprecation": {"type": "string"}, "profile": {"type": "string"}, "name": {"type": "string"}}}, "PageMetadata": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int64"}, "number": {"type": "integer", "format": "int64"}}}, "PagedModelEntityModelAccountBlacklistSummary": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelAccountBlacklistSummary"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "VulnerabilitySummary": {"type": "object", "properties": {"veryHigh": {"type": "integer", "format": "int32"}, "high": {"type": "integer", "format": "int32"}, "medium": {"type": "integer", "format": "int32"}, "low": {"type": "integer", "format": "int32"}, "veryLow": {"type": "integer", "format": "int32"}, "informational": {"type": "integer", "format": "int32"}, "veryHighMitigated": {"type": "integer", "format": "int32"}, "highMitigated": {"type": "integer", "format": "int32"}, "mediumMitigated": {"type": "integer", "format": "int32"}, "lowMitigated": {"type": "integer", "format": "int32"}, "veryLowMitigated": {"type": "integer", "format": "int32"}, "informationalMitigated": {"type": "integer", "format": "int32"}, "highestScore": {"type": "number", "format": "float"}}}, "AccountBlacklist": {"required": ["account_id", "component_id"], "type": "object", "properties": {"account_id": {"type": "integer", "format": "int64"}, "component_id": {"type": "string"}, "remediation_text": {"maxLength": 2048, "minLength": 0, "type": "string"}, "blacklisted": {"type": "boolean"}, "state_changed": {"type": "boolean"}, "blocklisted": {"type": "boolean"}}}, "PagedResourcesAssemblerLinkedAppPortfolioResult_Portfolio": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "EntityModelLinkedAppPortfolioResult_Portfolio": {"type": "object", "properties": {"app_id": {"type": "integer", "format": "int64"}, "violated_components": {"type": "integer", "format": "int32"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_Portfolio"}, "account_id": {"type": "integer", "format": "int64"}, "latest_scan_id": {"type": "string"}, "num_projects": {"type": "integer", "format": "int32"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}}}, "PagedModelEntityModelLinkedAppPortfolioResult_Portfolio": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelLinkedAppPortfolioResult_Portfolio"}}, "page": {"$ref": "#/components/schemas/PageMetadata_Portfolio"}}}, "PortfolioRequest": {"type": "object", "properties": {"app_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "cve_id": {"type": "string"}, "license_name": {"type": "string"}, "has_license": {"type": "boolean"}, "file_name": {"type": "string"}, "blacklist": {"type": "boolean"}, "severities": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}, "risk_ratings": {"type": "array", "items": {"type": "string"}}, "has_license_risk_rating": {"type": "boolean"}, "sort": {"type": "string"}, "size": {"maximum": 2000, "type": "integer", "format": "int32"}, "page": {"maximum": 2000, "type": "integer", "format": "int32"}, "cvss_version": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}}}, "PagedResourcesAssemblerApplicationResult": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "EntityModelApplicationResult": {"type": "object", "properties": {"account_id": {"type": "integer", "format": "int64"}, "app_id": {"type": "integer", "format": "int64"}, "component_id": {"type": "string"}, "latest_scan_id": {"type": "string"}, "group_id": {"type": "string"}, "module_id": {"type": "string"}, "artifact_version": {"type": "string"}, "module_name": {"type": "string"}, "description": {"type": "string"}, "artifact_license": {"type": "string"}, "artifact_url": {"type": "string"}, "file_name": {"type": "string"}, "file_path": {"type": "string"}, "sha1": {"type": "string"}, "license": {"type": "string"}, "submitted_ts": {"type": "string", "format": "date-time"}, "cve_id": {"type": "string"}, "cwe_id": {"type": "string"}, "cve_summary": {"type": "string"}, "cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "cve_access_vector": {"type": "string"}, "cve_access_complexity": {"type": "string"}, "cve_authentication": {"type": "string"}, "cve_availability_impact": {"type": "string"}, "cve_confidentiality_impact": {"type": "string"}, "cve_published_datetime": {"type": "string", "format": "date-time"}, "cve_very_high": {"type": "integer", "format": "int32"}, "cve_high": {"type": "integer", "format": "int32"}, "cve_medium": {"type": "integer", "format": "int32"}, "cve_low": {"type": "integer", "format": "int32"}, "cve_very_low": {"type": "integer", "format": "int32"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "PagedModelEntityModelApplicationResult": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelApplicationResult"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "ShareScanKey": {"type": "object", "properties": {"scan_id": {"type": "string"}, "app_id": {"type": "integer", "format": "int64"}, "account_id": {"type": "integer", "format": "int64"}}}, "ShareScan": {"type": "object", "properties": {"app_id": {"type": "integer", "format": "int64"}, "account_id": {"type": "integer", "format": "int64"}, "scan_id": {"type": "string"}}}, "ScanRequest": {"required": ["account_id", "analysis_unit_id", "app_id", "app_ver_id", "job_id", "sandbox", "sandbox_id", "sca_enabled", "scan_config_id"], "type": "object", "properties": {"scan_config_id": {"type": "string"}, "account_id": {"type": "integer", "format": "int64"}, "job_id": {"type": "integer", "format": "int64"}, "app_id": {"type": "integer", "format": "int64"}, "sandbox_id": {"type": "integer", "format": "int64"}, "app_ver_id": {"type": "integer", "format": "int64"}, "analysis_unit_id": {"type": "integer", "format": "int64"}, "sandbox": {"type": "boolean"}, "sca_enabled": {"type": "boolean"}, "async": {"type": "boolean"}}}, "DeleteProjects": {"required": ["project_ids"], "type": "object", "properties": {"project_ids": {"type": "array", "items": {"type": "string"}}}}, "AdvancedOptions": {"type": "object", "properties": {"blocklist": {"type": "boolean"}, "allowed_nonoss_licenses": {"type": "boolean"}, "allowed_unrecognized_licenses": {"type": "boolean"}, "all_licenses_must_meet_requirement": {"type": "boolean"}, "is_blocklist": {"type": "boolean"}, "selected_licenses_list": {"type": "array", "items": {"type": "string"}}}}, "ComponentAppRequest": {"required": ["component_id"], "type": "object", "properties": {"app_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "cve_id": {"type": "string"}, "license_name": {"type": "string"}, "has_license": {"type": "boolean"}, "file_name": {"type": "string"}, "blacklist": {"type": "boolean"}, "severities": {"type": "array", "items": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}}, "risk_ratings": {"type": "array", "items": {"type": "string"}}, "has_license_risk_rating": {"type": "boolean"}, "sort": {"type": "string"}, "size": {"maximum": 2000, "type": "integer", "format": "int32"}, "page": {"maximum": 2000, "type": "integer", "format": "int32"}, "cvss_version": {"type": "string", "enum": ["CVSS_VERSION_2", "CVSS_VERSION_3"]}, "component_id": {"pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$", "type": "string"}, "policy": {"$ref": "#/components/schemas/ScanPolicy"}}}, "CvssGracePeriodDays": {"type": "object", "properties": {"lower": {"type": "number", "format": "float"}, "upper": {"type": "number", "format": "float"}, "days": {"type": "integer", "format": "int32"}}}, "PolicyRule": {"type": "object", "properties": {"type": {"type": "string", "enum": ["SCA_BLACKLIST", "SCA_CVSS", "SCA_SEVERITY", "SCA_RISKRATING"]}, "reference_id": {"type": "number", "format": "float"}, "advanced_options": {"$ref": "#/components/schemas/AdvancedOptions"}}}, "ScanPolicy": {"type": "object", "properties": {"policy_id": {"type": "integer", "format": "int64"}, "account_id": {"type": "integer", "format": "int64"}, "policy_name": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "rules": {"type": "array", "items": {"$ref": "#/components/schemas/PolicyRule"}}, "blacklist_grace_period_days": {"type": "integer", "format": "int32"}, "severity_grace_periods_days": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "evaluation_date": {"type": "string", "format": "date-time"}, "evaluation_date_type": {"type": "string", "enum": ["BEFORE", "AFTER"]}, "risk_rating_grace_period_days": {"type": "integer", "format": "int32"}, "cvss_grace_period_days": {"type": "array", "items": {"$ref": "#/components/schemas/CvssGracePeriodDays"}}}}, "ArtifactFilePortfolio": {"type": "object", "properties": {"artifact_id": {"type": "string"}, "version": {"type": "string"}, "id": {"type": "string"}, "file_name": {"type": "string"}, "in_portfolio": {"type": "boolean"}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "artifact_name": {"type": "string"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary"}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "blocklisted": {"type": "boolean"}}}, "CollectionModelArtifactFilePortfolio": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ArtifactFilePortfolio"}}}}, "PolicyCompliance": {"required": ["policy_id", "policy_name", "scan_id"], "type": "object", "properties": {"scan_id": {"type": "string"}, "policy_id": {"type": "integer", "format": "int64"}, "policy_name": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "violated_components": {"type": "integer", "format": "int32"}, "mitigated_components": {"type": "integer", "format": "int32"}, "policy_status": {"type": "string", "enum": ["DETERMINING", "NOT_ASSESSED", "DID_NOT_PASS", "CONDITIONAL_PASS", "PASSED"]}, "grace_ts": {"type": "string", "format": "date-time"}, "rules": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/PolicyComplianceRule"}}}}, "PolicyComplianceRule": {"required": ["reference_id", "rule_type"], "type": "object", "properties": {"rule_type": {"type": "string", "enum": ["SCA_BLACKLIST", "SCA_CVSS", "SCA_SEVERITY", "SCA_RISKRATING"]}, "reference_id": {"type": "number", "format": "float"}, "violated_components": {"type": "integer", "format": "int32"}, "mitigated_components": {"type": "integer", "format": "int32"}, "grace_period_expired_components": {"type": "integer", "format": "int32"}, "grace_ts": {"type": "string", "format": "date-time"}}}, "CollectionModelLinkedAppPortfolio": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedAppPortfolio"}}}}, "LinkedAppPortfolio": {"type": "object", "properties": {"app_id": {"type": "integer", "format": "int64"}, "latest_scan_id": {"type": "string"}, "account_id": {"type": "integer", "format": "int64"}}}, "BulkMitigation": {"required": ["action", "comments", "mitigations"], "type": "object", "properties": {"action": {"type": "string", "enum": ["COMMENT", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE", "REJECT", "APPROVE"]}, "comments": {"type": "string"}, "mitigations": {"type": "array", "items": {"$ref": "#/components/schemas/Mitigation"}}}}, "Mitigation": {"required": ["comments", "component_id", "deleted", "mitigation_action", "mitigation_reason", "mitigation_status", "user_account_id", "user_name"], "type": "object", "properties": {"mitigation_id": {"type": "string"}, "component_id": {"type": "string"}, "cve_id": {"type": "string"}, "license_id": {"type": "string"}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "mitigation_action": {"type": "string", "enum": ["COMMENT", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE", "REJECT", "APPROVE"]}, "comments": {"maxLength": 2048, "minLength": 0, "type": "string"}, "deleted": {"type": "boolean"}, "user_name": {"type": "string"}, "user_account_name": {"type": "string"}, "user_account_id": {"type": "integer", "format": "int32"}, "license_risk_rating": {"type": "string"}, "mitigation_status_text": {"type": "string"}, "mitigation_ts": {"type": "string", "format": "date-time"}}}, "BulkMitigationResult": {"type": "object", "properties": {"mitigations": {"type": "array", "items": {"$ref": "#/components/schemas/Mitigation"}}, "state_changed": {"type": "boolean"}}}, "AppProject": {"required": ["app_id", "deleted", "linked", "project_id"], "type": "object", "properties": {"app_project_id": {"type": "string"}, "project_id": {"pattern": "^[0-9]+$", "type": "string"}, "project_name": {"type": "string"}, "app_id": {"type": "integer", "format": "int64"}, "workspace_name": {"type": "string"}, "default_branch": {"type": "string"}, "linked": {"type": "boolean"}, "linked_ts": {"type": "string", "format": "date-time"}, "unlinked_ts": {"type": "string", "format": "date-time"}, "deleted": {"type": "boolean"}, "delete_ts": {"type": "string", "format": "date-time"}, "event_type": {"type": "string", "enum": ["LINK", "UNLINK"]}}}, "PagedResourcesAssemblerAppPortfolioResult_Portfolio": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "EntityModelAppPortfolioResult_Portfolio": {"type": "object", "properties": {"app_id": {"type": "integer", "format": "int64"}, "violated_components": {"type": "integer", "format": "int32"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_Portfolio"}, "account_id": {"type": "integer", "format": "int64"}, "latest_scan_id": {"type": "string"}, "num_projects": {"type": "integer", "format": "int32"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}}}, "PagedModelEntityModelAppPortfolioResult_Portfolio": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_Portfolio"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelAppPortfolioResult_Portfolio"}}, "page": {"$ref": "#/components/schemas/PageMetadata_Portfolio"}}}, "Pageable": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "unpaged": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/Sort"}, "offset": {"type": "integer", "format": "int64"}}}, "Sort": {"type": "object", "properties": {"sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PagedResourcesAssemblerAppScanFindingWrapper": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "Annotation": {"type": "object", "properties": {"mitigationStatus": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigationAction": {"type": "string", "enum": ["COMMENT", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE", "REJECT", "APPROVE"]}, "comment": {"type": "string"}, "userName": {"type": "string"}, "mitigation_id": {"type": "string"}, "created": {"type": "string", "format": "date-time"}}}, "CVE": {"type": "object", "properties": {"name": {"type": "string"}, "cvss": {"type": "number", "format": "float"}, "cwe": {"type": "string"}, "href": {"type": "string"}, "severity": {"type": "string"}, "vector": {"type": "string"}, "cvss3": {"$ref": "#/components/schemas/CVSS3"}}}, "CVSS3": {"type": "object", "properties": {"score": {"type": "number", "format": "float"}, "severity": {"type": "string"}, "vector": {"type": "string"}}}, "ComponentPath": {"type": "object", "properties": {"path": {"type": "string"}}}, "EntityModelAppScanFindingWrapper": {"type": "object", "properties": {"scan_id": {"type": "string"}, "sandbox_id": {"type": "integer", "format": "int64"}, "context_type": {"type": "string"}, "scan_date": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "cve": {"$ref": "#/components/schemas/CVE"}, "severity": {"type": "integer", "format": "int32"}, "scan_type": {"type": "string"}, "latest_mitigation_status": {"type": "string"}, "violates_policy": {"type": "boolean"}, "component_filename": {"type": "string"}, "component_id": {"type": "string"}, "product_id": {"type": "string"}, "first_found_date": {"type": "string", "format": "date-time"}, "version": {"type": "string"}, "language": {"type": "string"}, "component_path": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentPath"}}, "licenses": {"type": "array", "items": {"$ref": "#/components/schemas/License"}}, "metadata": {"$ref": "#/components/schemas/FindingMetadata"}, "annotations": {"type": "array", "items": {"$ref": "#/components/schemas/Annotation"}}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "FindingMetadata": {"type": "object", "properties": {"sca_scan_mode": {"type": "string"}, "sca_dep_mode": {"type": "string"}}}, "License": {"type": "object", "properties": {"licenseId": {"type": "string"}, "riskRating": {"type": "string"}}}, "PagedModelEntityModelAppScanFindingWrapper": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelAppScanFindingWrapper"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "CollectionModelLinkedCommonScanVulnerability": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedCommonScanVulnerability"}}}}, "LinkedCommonScanVulnerability": {"type": "object", "properties": {"policyCompliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "entityId": {"type": "string"}, "mitigated": {"type": "boolean"}, "cveScore": {"type": "number", "format": "float"}, "cveSeverity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "violatePolicy": {"type": "boolean"}, "firstFoundDate": {"type": "string", "format": "date-time"}, "cveId": {"type": "string"}}}, "AdvancedOptions_ComponentDetail": {"type": "object", "properties": {"blocklist": {"type": "boolean"}, "allowed_nonoss_licenses": {"type": "boolean"}, "allowed_unrecognized_licenses": {"type": "boolean"}, "all_licenses_must_meet_requirement": {"type": "boolean"}, "is_blocklist": {"type": "boolean"}, "selected_licenses_list": {"type": "array", "items": {"type": "string"}}}}, "CvssGracePeriodDays_ComponentDetail": {"type": "object", "properties": {"lower": {"type": "number", "format": "float"}, "upper": {"type": "number", "format": "float"}, "days": {"type": "integer", "format": "int32"}}}, "PolicyRule_ComponentDetail": {"type": "object", "properties": {"type": {"type": "string", "enum": ["SCA_BLACKLIST", "SCA_CVSS", "SCA_SEVERITY", "SCA_RISKRATING"]}, "reference_id": {"type": "number", "format": "float"}, "advanced_options": {"$ref": "#/components/schemas/AdvancedOptions_ComponentDetail"}}}, "ScanPolicy_ComponentDetail": {"type": "object", "properties": {"policy_id": {"type": "integer", "format": "int64"}, "account_id": {"type": "integer", "format": "int64"}, "policy_name": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "rules": {"type": "array", "items": {"$ref": "#/components/schemas/PolicyRule_ComponentDetail"}}, "blacklist_grace_period_days": {"type": "integer", "format": "int32"}, "severity_grace_periods_days": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "evaluation_date": {"type": "string", "format": "date-time"}, "evaluation_date_type": {"type": "string", "enum": ["BEFORE", "AFTER"]}, "risk_rating_grace_period_days": {"type": "integer", "format": "int32"}, "cvss_grace_period_days": {"type": "array", "items": {"$ref": "#/components/schemas/CvssGracePeriodDays_ComponentDetail"}}}}, "Artifact_ComponentDetail": {"required": ["artifact_source", "version"], "type": "object", "properties": {"artifact_source": {"type": "string", "enum": ["MAVEN", "NUGET", "NPM", "BOWER", "COCOAPODS", "GEM", "SO", "PACKAGIST", "GO", "PYPI", "RPM", "COCOAPODS1", "MANUAL", "ALPINE", "DOTNETCORE", "DEBIAN"]}, "version": {"type": "string"}, "group_id": {"type": "string"}, "module_id": {"type": "string"}, "description": {"type": "string"}, "module_name": {"type": "string"}, "project_url": {"type": "string"}}}, "LinkedScanComponentPath_ComponentDetail": {"type": "object", "properties": {"file_path": {"type": "string"}, "sha1": {"type": "string"}}}, "LinkedScanComponent_ComponentDetail": {"type": "object", "properties": {"component_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "is_new": {"type": "boolean"}, "last_seen_ts": {"type": "string", "format": "date-time"}, "occurrences": {"type": "integer", "format": "int32"}, "paths": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/LinkedScanComponentPath_ComponentDetail"}}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentDetail"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_ComponentDetail"}, "vulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedScanVulnerability_ComponentDetail"}}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "artifact": {"$ref": "#/components/schemas/Artifact_ComponentDetail"}, "product_name": {"type": "string"}, "blocklisted": {"type": "boolean"}, "mitigated": {"type": "boolean"}}}, "LinkedScanResult_ComponentDetail": {"type": "object", "properties": {"scan_id": {"type": "string"}, "prev_scan_id": {"type": "string"}, "scan_completed_ts": {"type": "string", "format": "date-time"}, "component_count": {"type": "integer", "format": "int32"}, "violated_components": {"type": "integer", "format": "int32"}, "policy_status": {"type": "string", "enum": ["DETERMINING", "NOT_ASSESSED", "DID_NOT_PASS", "CONDITIONAL_PASS", "PASSED"]}, "scan_components": {"type": "array", "items": {"$ref": "#/components/schemas/ScanComponentResultModel_ComponentDetail"}}, "removed_components": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedScanComponent_ComponentDetail"}}, "blacklisted_components": {"type": "integer", "format": "int32"}}}, "LinkedScanVulnerability_ComponentDetail": {"type": "object", "properties": {"component_id": {"type": "string"}, "cve_id": {"type": "string"}, "violate_policy": {"type": "boolean"}, "cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "vuln_title": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "cve_summary": {"type": "string"}, "cwe_id": {"type": "string"}, "cwe_category": {"type": "string"}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentDetail"}, "first_found_date": {"type": "string", "format": "date-time"}, "mitigations": {"type": "array", "items": {"$ref": "#/components/schemas/MitigationModel_ComponentDetail"}}, "mitigated": {"type": "boolean"}, "mitigated_ts": {"type": "string", "format": "date-time"}, "mitigation_status_text": {"type": "string"}}}, "MitigationModel_ComponentDetail": {"type": "object", "properties": {"mitigation_action": {"type": "string"}, "comment": {"type": "string"}, "user": {"type": "string"}, "date": {"type": "string", "format": "date-time"}}}, "PolicyComplianceRule_ComponentDetail": {"required": ["reference_id", "rule_type"], "type": "object", "properties": {"rule_type": {"type": "string", "enum": ["SCA_BLACKLIST", "SCA_CVSS", "SCA_SEVERITY", "SCA_RISKRATING"]}, "reference_id": {"type": "number", "format": "float"}, "violated_components": {"type": "integer", "format": "int32"}, "mitigated_components": {"type": "integer", "format": "int32"}, "grace_period_expired_components": {"type": "integer", "format": "int32"}, "grace_ts": {"type": "string", "format": "date-time"}}}, "PolicyCompliance_ComponentDetail": {"required": ["policy_id", "policy_name", "scan_id"], "type": "object", "properties": {"scan_id": {"type": "string"}, "policy_id": {"type": "integer", "format": "int64"}, "policy_name": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "violated_components": {"type": "integer", "format": "int32"}, "mitigated_components": {"type": "integer", "format": "int32"}, "policy_status": {"type": "string", "enum": ["DETERMINING", "NOT_ASSESSED", "DID_NOT_PASS", "CONDITIONAL_PASS", "PASSED"]}, "grace_ts": {"type": "string", "format": "date-time"}, "rules": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/PolicyComplianceRule_ComponentDetail"}}}}, "ScanComponentResultModel_ComponentDetail": {"type": "object", "properties": {"component_id": {"type": "string"}, "name": {"type": "string"}, "unique_lookup_value": {"type": "string"}, "product_name": {"type": "string"}, "scan_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "last_seen_ts": {"type": "string", "format": "date-time"}, "occurrences": {"type": "integer", "format": "int32"}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "paths": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/LinkedScanComponentPath_ComponentDetail"}}, "licenses": {"type": "array", "items": {"$ref": "#/components/schemas/ScanLicenseModel_ComponentDetail"}}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentDetail"}, "vulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedScanVulnerability_ComponentDetail"}}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_ComponentDetail"}, "artifact": {"$ref": "#/components/schemas/Artifact_ComponentDetail"}, "mitigated": {"type": "boolean"}}}, "ScanLicenseModel_ComponentDetail": {"type": "object", "properties": {"license_id": {"type": "string"}, "component_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}, "mitigated": {"type": "boolean"}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentDetail"}, "mitigation_insert_ts": {"type": "string", "format": "date-time"}, "mitigations": {"type": "array", "items": {"$ref": "#/components/schemas/MitigationModel_ComponentDetail"}}}}, "VulnerabilitySummary_ComponentDetail": {"type": "object", "properties": {"veryHigh": {"type": "integer", "format": "int32"}, "high": {"type": "integer", "format": "int32"}, "medium": {"type": "integer", "format": "int32"}, "low": {"type": "integer", "format": "int32"}, "veryLow": {"type": "integer", "format": "int32"}, "informational": {"type": "integer", "format": "int32"}, "veryHighMitigated": {"type": "integer", "format": "int32"}, "highMitigated": {"type": "integer", "format": "int32"}, "mediumMitigated": {"type": "integer", "format": "int32"}, "lowMitigated": {"type": "integer", "format": "int32"}, "veryLowMitigated": {"type": "integer", "format": "int32"}, "informationalMitigated": {"type": "integer", "format": "int32"}, "highestScore": {"type": "number", "format": "float"}}}, "CollectionModelScanLicenseModel": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ScanLicenseModel"}}}}, "MitigationModel": {"type": "object", "properties": {"mitigation_action": {"type": "string"}, "comment": {"type": "string"}, "user": {"type": "string"}, "date": {"type": "string", "format": "date-time"}}}, "ScanLicenseModel": {"type": "object", "properties": {"license_id": {"type": "string"}, "component_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}, "mitigated": {"type": "boolean"}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "mitigation_insert_ts": {"type": "string", "format": "date-time"}, "mitigations": {"type": "array", "items": {"$ref": "#/components/schemas/MitigationModel"}}}}, "AdvancedOptions_ComponentSummary": {"type": "object", "properties": {"blocklist": {"type": "boolean"}, "allowed_nonoss_licenses": {"type": "boolean"}, "allowed_unrecognized_licenses": {"type": "boolean"}, "all_licenses_must_meet_requirement": {"type": "boolean"}, "is_blocklist": {"type": "boolean"}, "selected_licenses_list": {"type": "array", "items": {"type": "string"}}}}, "CvssGracePeriodDays_ComponentSummary": {"type": "object", "properties": {"lower": {"type": "number", "format": "float"}, "upper": {"type": "number", "format": "float"}, "days": {"type": "integer", "format": "int32"}}}, "PolicyRule_ComponentSummary": {"type": "object", "properties": {"type": {"type": "string", "enum": ["SCA_BLACKLIST", "SCA_CVSS", "SCA_SEVERITY", "SCA_RISKRATING"]}, "reference_id": {"type": "number", "format": "float"}, "advanced_options": {"$ref": "#/components/schemas/AdvancedOptions_ComponentSummary"}}}, "ScanPolicy_ComponentSummary": {"type": "object", "properties": {"policy_id": {"type": "integer", "format": "int64"}, "account_id": {"type": "integer", "format": "int64"}, "policy_name": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "rules": {"type": "array", "items": {"$ref": "#/components/schemas/PolicyRule_ComponentSummary"}}, "blacklist_grace_period_days": {"type": "integer", "format": "int32"}, "severity_grace_periods_days": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "evaluation_date": {"type": "string", "format": "date-time"}, "evaluation_date_type": {"type": "string", "enum": ["BEFORE", "AFTER"]}, "risk_rating_grace_period_days": {"type": "integer", "format": "int32"}, "cvss_grace_period_days": {"type": "array", "items": {"$ref": "#/components/schemas/CvssGracePeriodDays_ComponentSummary"}}}}, "Artifact_ComponentSummary": {"required": ["artifact_source", "version"], "type": "object", "properties": {"artifact_source": {"type": "string", "enum": ["MAVEN", "NUGET", "NPM", "BOWER", "COCOAPODS", "GEM", "SO", "PACKAGIST", "GO", "PYPI", "RPM", "COCOAPODS1", "MANUAL", "ALPINE", "DOTNETCORE", "DEBIAN"]}, "version": {"type": "string"}, "group_id": {"type": "string"}, "module_id": {"type": "string"}, "description": {"type": "string"}, "module_name": {"type": "string"}, "project_url": {"type": "string"}}}, "CollectionModelEntityModelScanComponentModel_ComponentSummary": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_ComponentSummary"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelScanComponentModel_ComponentSummary"}}}}, "EntityModelScanComponentModel_ComponentSummary": {"type": "object", "properties": {"component_id": {"type": "string"}, "product_name": {"type": "string"}, "scan_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "last_seen_ts": {"type": "string", "format": "date-time"}, "occurrences": {"type": "integer", "format": "int32"}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "licenses": {"type": "array", "items": {"$ref": "#/components/schemas/LicenseModel_ComponentSummary"}}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentSummary"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_ComponentSummary"}, "paths": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/LinkedScanComponentPath_ComponentSummary"}}, "artifact": {"$ref": "#/components/schemas/Artifact_ComponentSummary"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link_ComponentSummary"}}}}, "LicenseModel_ComponentSummary": {"type": "object", "properties": {"license_id": {"type": "string"}, "spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}, "mitigated": {"type": "boolean"}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}}}, "Link_ComponentSummary": {"type": "object", "properties": {"rel": {"type": "string"}, "href": {"type": "string"}, "hreflang": {"type": "string"}, "media": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "deprecation": {"type": "string"}, "profile": {"type": "string"}, "name": {"type": "string"}}}, "LinkedScanComponentPath_ComponentSummary": {"type": "object", "properties": {"file_path": {"type": "string"}, "sha1": {"type": "string"}}}, "PolicyComplianceRule_ComponentSummary": {"required": ["reference_id", "rule_type"], "type": "object", "properties": {"rule_type": {"type": "string", "enum": ["SCA_BLACKLIST", "SCA_CVSS", "SCA_SEVERITY", "SCA_RISKRATING"]}, "reference_id": {"type": "number", "format": "float"}, "violated_components": {"type": "integer", "format": "int32"}, "mitigated_components": {"type": "integer", "format": "int32"}, "grace_period_expired_components": {"type": "integer", "format": "int32"}, "grace_ts": {"type": "string", "format": "date-time"}}}, "PolicyCompliance_ComponentSummary": {"required": ["policy_id", "policy_name", "scan_id"], "type": "object", "properties": {"scan_id": {"type": "string"}, "policy_id": {"type": "integer", "format": "int64"}, "policy_name": {"type": "string"}, "version": {"type": "integer", "format": "int32"}, "violated_components": {"type": "integer", "format": "int32"}, "mitigated_components": {"type": "integer", "format": "int32"}, "policy_status": {"type": "string", "enum": ["DETERMINING", "NOT_ASSESSED", "DID_NOT_PASS", "CONDITIONAL_PASS", "PASSED"]}, "grace_ts": {"type": "string", "format": "date-time"}, "rules": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/PolicyComplianceRule_ComponentSummary"}}}}, "VulnerabilitySummary_ComponentSummary": {"type": "object", "properties": {"veryHigh": {"type": "integer", "format": "int32"}, "high": {"type": "integer", "format": "int32"}, "medium": {"type": "integer", "format": "int32"}, "low": {"type": "integer", "format": "int32"}, "veryLow": {"type": "integer", "format": "int32"}, "informational": {"type": "integer", "format": "int32"}, "veryHighMitigated": {"type": "integer", "format": "int32"}, "highMitigated": {"type": "integer", "format": "int32"}, "mediumMitigated": {"type": "integer", "format": "int32"}, "lowMitigated": {"type": "integer", "format": "int32"}, "veryLowMitigated": {"type": "integer", "format": "int32"}, "informationalMitigated": {"type": "integer", "format": "int32"}, "highestScore": {"type": "number", "format": "float"}}}, "Artifact": {"required": ["artifact_source", "version"], "type": "object", "properties": {"artifact_source": {"type": "string", "enum": ["MAVEN", "NUGET", "NPM", "BOWER", "COCOAPODS", "GEM", "SO", "PACKAGIST", "GO", "PYPI", "RPM", "COCOAPODS1", "MANUAL", "ALPINE", "DOTNETCORE", "DEBIAN"]}, "version": {"type": "string"}, "group_id": {"type": "string"}, "module_id": {"type": "string"}, "description": {"type": "string"}, "module_name": {"type": "string"}, "project_url": {"type": "string"}}}, "LicenseModel": {"type": "object", "properties": {"license_id": {"type": "string"}, "spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}, "mitigated": {"type": "boolean"}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}}}, "LinkedScanComponentPath": {"type": "object", "properties": {"file_path": {"type": "string"}, "sha1": {"type": "string"}}}, "ScanComponentModel": {"type": "object", "properties": {"component_id": {"type": "string"}, "product_name": {"type": "string"}, "scan_id": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "last_seen_ts": {"type": "string", "format": "date-time"}, "occurrences": {"type": "integer", "format": "int32"}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "licenses": {"type": "array", "items": {"$ref": "#/components/schemas/LicenseModel"}}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary"}, "paths": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/LinkedScanComponentPath"}}, "artifact": {"$ref": "#/components/schemas/Artifact"}}}, "CollectionModelLinkedScanVulnerability": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/LinkedScanVulnerability"}}}}, "LinkedScanVulnerability": {"type": "object", "properties": {"component_id": {"type": "string"}, "cve_id": {"type": "string"}, "violate_policy": {"type": "boolean"}, "cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "vuln_title": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "cve_summary": {"type": "string"}, "cwe_id": {"type": "string"}, "cwe_category": {"type": "string"}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "first_found_date": {"type": "string", "format": "date-time"}, "mitigations": {"type": "array", "items": {"$ref": "#/components/schemas/MitigationModel"}}, "mitigated": {"type": "boolean"}, "mitigated_ts": {"type": "string", "format": "date-time"}, "mitigation_status_text": {"type": "string"}}}, "PagedResourcesAssemblerBlocklistDetail": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "BlocklistDetailResource": {"type": "object", "properties": {"vulnerabilitySummary": {"$ref": "#/components/schemas/VulnerabilitySummary"}, "componentId": {"type": "string"}, "version": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "fileName": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "PagedModelBlocklistDetailResource": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/BlocklistDetailResource"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "CollectionModelScanVulnerability": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ScanVulnerability"}}}}, "ScanVulnerability": {"type": "object", "properties": {"component_id": {"type": "string"}, "cve_id": {"type": "string"}, "violate_policy": {"type": "boolean"}, "cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "vuln_title": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "cve_summary": {"type": "string"}, "cwe_id": {"type": "string"}, "cwe_category": {"type": "string"}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "first_found_date": {"type": "string", "format": "date-time"}, "mitigated": {"type": "boolean"}, "mitigated_ts": {"type": "string", "format": "date-time"}, "mitigation_status_text": {"type": "string"}}}, "ScanComponentPath_ComponentDetail": {"type": "object", "properties": {"file_path": {"type": "string"}, "sha1": {"type": "string"}}}, "ScanComponent_ComponentDetail": {"type": "object", "properties": {"component_id": {"type": "string"}, "file_name": {"type": "string"}, "product_name": {"type": "string"}, "version": {"type": "string"}, "is_new": {"type": "boolean"}, "blacklisted": {"type": "boolean"}, "last_seen_ts": {"type": "string", "format": "date-time"}, "paths": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ScanComponentPath_ComponentDetail"}}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentDetail"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary_ComponentDetail"}, "vulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/ScanVulnerability_ComponentDetail"}}, "remediation_text": {"type": "string"}, "artifact": {"$ref": "#/components/schemas/Artifact_ComponentDetail"}, "licenses": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/SpdxLicense_ComponentDetail"}}, "blocklisted": {"type": "boolean"}, "mitigated": {"type": "boolean"}}}, "ScanResult_ComponentDetail": {"type": "object", "properties": {"scan_id": {"type": "string"}, "prev_scan_id": {"type": "string"}, "scan_completed_ts": {"type": "string", "format": "date-time"}, "component_count": {"type": "integer", "format": "int32"}, "violated_components": {"type": "integer", "format": "int32"}, "policy_status": {"type": "string", "enum": ["DETERMINING", "NOT_ASSESSED", "DID_NOT_PASS", "CONDITIONAL_PASS", "PASSED"]}, "scan_components": {"type": "array", "items": {"$ref": "#/components/schemas/ScanComponent_ComponentDetail"}}, "removed_components": {"type": "array", "items": {"$ref": "#/components/schemas/ScanComponent_ComponentDetail"}}, "blacklisted_components": {"type": "integer", "format": "int32"}}}, "ScanVulnerability_ComponentDetail": {"type": "object", "properties": {"component_id": {"type": "string"}, "cve_id": {"type": "string"}, "violate_policy": {"type": "boolean"}, "cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "vuln_title": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "cve_summary": {"type": "string"}, "cwe_id": {"type": "string"}, "cwe_category": {"type": "string"}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance_ComponentDetail"}, "first_found_date": {"type": "string", "format": "date-time"}, "mitigated": {"type": "boolean"}, "mitigated_ts": {"type": "string", "format": "date-time"}, "mitigation_status_text": {"type": "string"}}}, "SpdxLicense_ComponentDetail": {"type": "object", "properties": {"spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}}}, "ComponentSource": {"type": "object", "properties": {"scan_id": {"type": "string"}, "source": {"type": "string"}, "source_type": {"type": "string"}, "workspace": {"type": "string"}}}, "ComponentSourceWrapper": {"type": "object", "properties": {"component_id": {"type": "string"}, "file_name": {"type": "string"}, "sources": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentSource"}}}}, "CollectionModelMitigation": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Mitigation"}}}}, "ScanComponent": {"type": "object", "properties": {"component_id": {"type": "string"}, "file_name": {"type": "string"}, "product_name": {"type": "string"}, "version": {"type": "string"}, "is_new": {"type": "boolean"}, "blacklisted": {"type": "boolean"}, "last_seen_ts": {"type": "string", "format": "date-time"}, "paths": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ScanComponentPath"}}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary"}, "vulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/ScanVulnerability"}}, "remediation_text": {"type": "string"}, "artifact": {"$ref": "#/components/schemas/Artifact"}, "licenses": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/SpdxLicense"}}, "blocklisted": {"type": "boolean"}, "mitigated": {"type": "boolean"}}}, "ScanComponentPath": {"type": "object", "properties": {"file_path": {"type": "string"}, "sha1": {"type": "string"}}}, "SpdxLicense": {"type": "object", "properties": {"spdx_id": {"type": "string"}, "license_name": {"type": "string"}, "name": {"type": "string"}, "license_url": {"type": "string"}, "risk_rating": {"type": "string"}}}, "Workspace": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createdDate": {"type": "string", "format": "date-time"}, "updatedDate": {"type": "string", "format": "date-time"}, "createdById": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "clientTeamId": {"type": "string"}, "policiesEnabled": {"type": "boolean"}, "repoCount": {"type": "integer", "format": "int32"}, "hidden": {"type": "boolean"}, "deleted": {"type": "boolean"}, "sandbox": {"type": "boolean"}, "apiOrgIdentifier": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "CollectionModelComponentCve": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentCve"}}}}, "ComponentCve": {"type": "object", "properties": {"cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "cve_id": {"type": "string"}, "vuln_title": {"type": "string"}, "cwe_id": {"type": "string"}, "cve_summary": {"type": "string"}, "mitigated": {"type": "boolean"}}}, "EntityModelComponent": {"required": ["unique_lookup_value"], "type": "object", "properties": {"unique_lookup_value": {"type": "string"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "blacklisted": {"type": "boolean"}, "remediation_text": {"type": "string"}, "vulnerability_summary": {"$ref": "#/components/schemas/VulnerabilitySummary"}, "licenses": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/SpdxLicense"}}, "product_name": {"type": "string"}, "component_vulnerabilities": {"type": "array", "items": {"$ref": "#/components/schemas/ComponentCve"}}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "CollectionModelEntityModelLinkedProject": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelLinkedProject"}}}}, "EntityModelLinkedProject": {"type": "object", "properties": {"app_id": {"type": "integer", "format": "int64"}, "project_id": {"type": "string"}, "project_name": {"type": "string"}, "workspace_name": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "AppMitigation": {"type": "object", "properties": {"component_id": {"type": "string"}, "cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "id": {"$ref": "#/components/schemas/AppMitigationKey"}, "file_name": {"type": "string"}, "version": {"type": "string"}, "cve_summary": {"type": "string"}, "cwe_id": {"type": "string"}, "cwe_category": {"type": "string"}, "mitigation_status": {"type": "string", "enum": ["NONE", "PROPOSED", "ACCEPTED", "REJECTED"]}, "mitigation_reason": {"type": "string", "enum": ["NONE", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE"]}, "mitigation_action": {"type": "string", "enum": ["COMMENT", "BYENV", "BYDESIGN", "FP", "ACCEPTRISK", "APPROVEBYLEGAL", "NOTSHIPPED", "EXPERIMENTAL", "INTERNALUSE", "REJECT", "APPROVE"]}, "mitigation_comments": {"type": "string"}, "policy_compliance": {"$ref": "#/components/schemas/PolicyCompliance"}, "user_name": {"type": "string"}, "user_account_name": {"type": "string"}, "insert_ts": {"type": "string", "format": "date-time"}, "mitigation_history": {"type": "array", "items": {"$ref": "#/components/schemas/Mitigation"}}, "cve_id": {"type": "string"}, "license_id": {"type": "string"}, "spdx_id": {"type": "string"}, "risk_rating": {"type": "string"}, "license_url": {"type": "string"}, "license_name": {"type": "string"}, "mitigated": {"type": "boolean"}}}, "AppMitigationKey": {"type": "object", "properties": {"componentId": {"type": "string"}, "mitigationId": {"type": "string"}, "applicationId": {"type": "integer", "format": "int64"}}}, "CollectionModelAppMitigation": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AppMitigation"}}}}, "LatestScanDetails": {"type": "object", "properties": {"account_id": {"type": "integer", "format": "int64"}, "scan_id": {"type": "string"}, "app_id": {"type": "integer", "format": "int64"}}}, "Alert": {"type": "object", "properties": {"alert_id": {"type": "string"}, "scan_id": {"type": "string"}, "alert_type": {"type": "string", "enum": ["SCAN_CREATED", "SCAN_DELETED", "SCAN_UNDELETED", "SCAN_COMPLETED", "NEW_SCAN_COMPONENT", "NEW_COMPONENT_CVE", "CVE_SEVERITY_CHANGE", "CVE_ID_CHANGE", "PROJECT_LINK", "PROJECT_UNLINK"]}, "published": {"type": "boolean"}, "policy_status": {"type": "string", "enum": ["DETERMINING", "NOT_ASSESSED", "DID_NOT_PASS", "CONDITIONAL_PASS", "PASSED"]}, "components": {"type": "array", "items": {"$ref": "#/components/schemas/AlertEntity"}}, "alert_ts": {"type": "string", "format": "date-time"}}}, "AlertEntity": {"type": "object", "properties": {"componentId": {"type": "string"}, "fileName": {"type": "string"}, "violatePolicy": {"type": "boolean"}, "violations": {"type": "array", "items": {"$ref": "#/components/schemas/AlertViolation"}}}}, "AlertViolation": {"type": "object", "properties": {"cve_score": {"type": "number", "format": "float"}, "cve_severity": {"type": "string", "enum": ["S0", "S1", "S2", "S3", "S4", "S5"]}, "cve_id": {"type": "string"}, "mitigated": {"type": "boolean"}}}, "CollectionModelAlert": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Alert"}}}}, "PagedResourcesAssemblerScanFindingWrapper": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "EntityModelScanFindingWrapper": {"type": "object", "properties": {"guid": {"type": "string"}, "cve": {"$ref": "#/components/schemas/CVE"}, "type": {"type": "string"}, "description": {"type": "string"}, "cwe": {"type": "string"}, "source": {"$ref": "#/components/schemas/Source"}, "severity": {"type": "integer", "format": "int32"}, "annotations": {"type": "array", "items": {"$ref": "#/components/schemas/Annotation"}}, "resolution": {"type": "string"}, "matched_id": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "PagedModelEntityModelScanFindingWrapper": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelScanFindingWrapper"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}, "Source": {"type": "object", "properties": {"version": {"type": "string"}, "language": {"type": "string"}, "componentId": {"type": "string"}, "component_filename": {"type": "string"}, "component_path": {"type": "string"}}}, "CollectionModelScanComponent": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/ScanComponent"}}}}, "EntityModelScanWrapper": {"type": "object", "properties": {"scan_type": {"type": "string"}, "sandbox_id": {"type": "integer", "format": "int64"}, "published": {"type": "string", "format": "date-time"}, "application_id": {"type": "integer", "format": "int64"}, "deleted": {"type": "boolean"}, "guid": {"type": "string"}, "status": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}}}, "PagedResourcesAssemblerScanWrapper": {"type": "object", "properties": {"forceFirstAndLastRels": {"type": "boolean", "writeOnly": true}}}, "PagedModelEntityModelScanWrapper": {"type": "object", "properties": {"links": {"type": "array", "items": {"$ref": "#/components/schemas/Link"}}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/EntityModelScanWrapper"}}, "page": {"$ref": "#/components/schemas/PageMetadata"}}}}}}