{"info": {"_postman_id": "bef9b8df-9c48-4d61-ad62-f01466ec7729", "name": "Findings API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12208008"}, "item": [{"name": "App_findings_info", "item": [{"name": "Get GUIDs", "request": {"method": "GET", "header": [], "url": {"raw": "{{hostname}}/appsec/v1/applications?org=10001", "host": ["{{hostname}}"], "path": ["appsec", "v1", "applications"], "query": [{"key": "org", "value": "10001"}]}}, "response": []}, {"name": "application_findings_info", "request": {"method": "GET", "header": [], "url": {"raw": "{{hostname}}/appsec/v2/applications/:application_guid/findings", "host": ["{{hostname}}"], "path": ["appsec", "v2", "applications", ":application_guid", "findings"], "variable": [{"key": "application_guid", "value": "e6640709-a996-489f-939f-b52deceb40d3"}]}}, "response": []}]}, {"name": "CWE_API_info", "item": [{"name": "Returns a list of CWE categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{hostname}}/appsec/v1/categories?page=0&size=10", "host": ["{{hostname}}"], "path": ["appsec", "v1", "categories"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Returns CWE Category info and links to associated resources", "request": {"method": "GET", "header": [], "url": {"raw": "{{hostname}}/appsec/v1/categories/:category", "host": ["{{hostname}}"], "path": ["appsec", "v1", "categories", ":category"], "variable": [{"key": "category", "value": "7"}]}}, "response": []}, {"name": "Returns a list of CWEs", "request": {"method": "GET", "header": [], "url": {"raw": "{{hostname}}/appsec/v1/cwes?page=0&size=10", "host": ["{{hostname}}"], "path": ["appsec", "v1", "cwes"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Return CW info and links to associated resources", "request": {"method": "GET", "header": [], "url": {"raw": "{{hostname}}/appsec/v1/cwes/:cwe", "host": ["{{hostname}}"], "path": ["appsec", "v1", "cwes", ":cwe"], "variable": [{"key": "cwe", "value": "327"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["var url = require('url');", "const id = pm.variables.get('api_key_id');", "const key = pm.variables.get('api_key_secret');", "const authorizationScheme = 'VERACODE-HMAC-SHA-256';", "const requestVersion = \"vcode_request_version_1\";", "const nonceSize = 16;", "function computeHashHex(message, key_hex) {", "    return CryptoJS.HmacSHA256(message, CryptoJS.enc.Hex.parse(key_hex)).toString(CryptoJS.enc.Hex);", "}", "function calulateDataSignature(key, nonceBytes, dateStamp, data) {", "    let kNonce = computeHashHex(nonceBytes, key);", "    let kDate = computeHashHex(dateStamp, kNonce);", "    let kSig = computeHashHex(requestVersion, kDate);", "    let kFinal = computeHashHex(data, kSig);", "    return kFinal;", "}", "function newNonce() {", "    return CryptoJS.lib.WordArray.random(nonceSize).toString().toUpperCase();", "}", "function toHexBinary(input) {", "    return CryptoJS.enc.Hex.stringify(CryptoJS.enc.Utf8.parse(input));", "}", "function calculateVeracodeAuthHeader(httpMethod, requestUrl) {", "    let urlExpanded = requestUrl;", "    while(urlExpanded.indexOf('{{') >= 0) {", "        let variableName = urlExpanded.substring(urlExpanded.indexOf('{{')+2, urlExpanded.indexOf('}}'));", "        let variableValue = pm.variables.get(variableName);", "        urlExpanded = urlExpanded.replace('{{'+variableName+'}}', variableValue);", "    }", "    let parsedUrl = url.parse(urlExpanded);", "    let data = `id=${id}&host=${parsedUrl.hostname}&url=${parsedUrl.path}&method=${httpMethod}`;", "    let dateStamp = Date.now().toString();", "    let nonceBytes = newNonce(nonceSize);", "    let dataSignature = calulateDataSignature(key, nonceBytes, dateStamp, data);", "    let authorizationParam = `id=${id},ts=${dateStamp},nonce=${toHexBinary(nonceBytes)},sig=${dataSignature}`;", "    let header = authorizationScheme + \" \" + authorizationParam;", "    return header;", "}", "pm.request.headers.add({", "    key: 'Authorization',", "    value: calculateVeracodeAuthHeader(request['method'], request['url'])", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}