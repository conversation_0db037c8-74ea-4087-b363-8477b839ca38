openapi: 3.0.1
info:
  title: SCA SBOM API
  description: SCA SBOM API for platform-backend
  version: "1.0"
servers:
  - url: https://scanresult-stage-151.stage.veracode.io/sca/scanresult
    description: Staging (152) server
  - url: https://scanresult.sca-prod.veracode.io/sca/scanresult
    description: Production server
paths:
  /applications/{application}/latestscan:
    get:
      tags:
        - get the latest scan API
      summary: get the latest scan information
      description: Returns the latest scan by passing an app id and an account id
      operationId: getLatestScanUsingGet
      parameters:
        - name: application
          in: path
          description: The encoded unique identifier of the workspace.
          required: true
          schema:
            type: integer
            format: int64
            nullable: false
            example: 1234
            description: Application id of Veracode platform
      responses:
        200:
          description: You have successfully get the data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LatestScanDetails'
        401:
          description: You are not authorized to perform this action.
          content: { }
        403:
          description: Access denied. You are not authorized to make this request.
          content: { }
        404:
          description: The app id/account id was not found.
          content: { }
        429:
          description: Request limit exceeded. You have sent too many requests in
            a single time period. Submit your request again later.
          content: { }
        500:
          description: Server-side error. Please try again later.
          content: { }
      deprecated: false

components:
  schemas:
    LatestScanDetails:
      type: object
      properties:
        account_id:
          type: integer
          format: int64
          nullable: false
          example: 12345
          description: Account id of Veracode platform
        app_id:
          type: integer
          format: int64
          nullable: false
          example: 1234
          description: Application id of Veracode platform
        scan_id:
          type: string
          format: uuid
          example: "c40f7b7e-1cdf-4cf8-883c-4a483d7cf02f"
          description: Latest scan id of an application for an account
