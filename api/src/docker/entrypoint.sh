#!/bin/bash
###############################################################################
# MAINTAINER <EMAIL> ######################################
###############################################################################

set -e

export CONFIG_DIR="/data/config"
mkdir -p "$CONFIG_DIR"

# configure aws command set default s3 sig to V4
aws configure set default.s3.signature_version s3v4

export KEY_STORE="${KEY_STORE:-service-keystore.jks}"
export TRUST_STORE="${TRUST_STORE:-service-truststore.jks}"
export MS_CONFIG_BUCKET="${MS_CONFIG_BUCKET:-sca-ms-config}"
# ENVIRONMENT : dev, qa, stage, prod
export ENVIRONMENT="${ENVIRONMENT:-dev}"

function die() { echo "$*"; exit 111; }

function pull_files_from_s3() {
    # ... Pull a single file from S3 using the "s3api" command. Requires 1 argument. Uses multiple variables.
    # aws s3 cp s3://"$MS_CONFIG_BUCKET/$PROJECT_NAME/$ENVIRONMENT/config" "$CONFIG_DIR" --recursive
    for file in `aws s3 ls s3://$MS_CONFIG_BUCKET/$PROJECT_NAME/$ENVIRONMENT/config/|awk '{print $4}'|grep -v '\.yml'`; do
	aws s3 cp s3://$MS_CONFIG_BUCKET/$PROJECT_NAME/$ENVIRONMENT/config/$file $CONFIG_DIR
    done
}

function check_for_basic_configuration () {
    # ... Check that required configuration files are found in config directory.
    # ... Function exits with not-OK status code, if not. (That's what the "egrep" part is for.)

    find "$CONFIG_DIR" -maxdepth 1 -name "$KEY_STORE" | egrep '.*' >/dev/null \
        || echo "Missing Java Keystore ($CONFIG_DIR/$KEY_STORE) "
    find "$CONFIG_DIR" -maxdepth 1 -name "$TRUST_STORE" | egrep '.*' >/dev/null \
        || echo "Missing Java Truststore ($CONFIG_DIR/$TRUST_STORE) "
    # REQUIRED. REQUIRED checks must come last and run as single command, so "return $?" will fail script if required is missing.
    find "$CONFIG_DIR" -maxdepth 1 -name 'application.yml' | egrep '.*' >/dev/null \
        || echo "Missing application config ($CONFIG_DIR/application.yml) "

    return "$?"
}

if [[ -z "$MS_CONFIG_BUCKET" ]]; then
    die 'CONFIG_BUCKET not specified. Will not pull config from S3.'
fi

if [[ -z "$PROJECT_NAME" ]]; then
    die 'PROJECT_NAME not specified. Will not pull config from S3.'
fi

pull_files_from_s3

check_for_basic_configuration || die "Missing config in $CONFIG_DIR. Set CONFIG_BUCKET or mount a volume to $CONFIG_DIR."

cmd="exec java $JAVA_OPTS -jar /data/${PROJECT_NAME}.jar"

echo "About to exec: $cmd"
$cmd
