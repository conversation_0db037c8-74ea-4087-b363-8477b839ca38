package com.veracode.scanresult.dto;

/**
 * The Class BlackListDTO.
 */
public class BlackListDTO {

	/** The high. */
	private Integer high;
	
	/** The component id. */
	private String component_id;
	
	/** The account id. */
	private Integer account_id;
	
	/** The very high. */
	private Integer very_high;
	
	/** The low. */
	private Integer low;
	
	/** The very low. */
	private Integer very_low;
	
	/** The file name. */
	private String file_name;
	
	/** The informational. */
	private Integer informational;
	
	/** The medium. */
	private Integer medium;
	
	/** The vulnerability summary. */
	private Object vulnerability_summary;
	
	/** The version. */
	private String version;
	
	/** The app count. */
	private Integer app_count;

	/**
	 * Gets the high.
	 *
	 * @return the high
	 */
	public Integer getHigh() {
		return high;
	}

	/**
	 * Sets the high.
	 *
	 * @param high the new high
	 */
	public void setHigh(Integer high) {
		this.high = high;
	}

	/**
	 * Gets the component id.
	 *
	 * @return the component id
	 */
	public String getComponent_id() {
		return component_id;
	}

	/**
	 * Sets the component id.
	 *
	 * @param component_id the new component id
	 */
	public void setComponent_id(String component_id) {
		this.component_id = component_id;
	}

	/**
	 * Gets the account id.
	 *
	 * @return the account id
	 */
	public Integer getAccount_id() {
		return account_id;
	}

	/**
	 * Sets the account id.
	 *
	 * @param account_id the new account id
	 */
	public void setAccount_id(Integer account_id) {
		this.account_id = account_id;
	}

	/**
	 * Gets the very high.
	 *
	 * @return the very high
	 */
	public Integer getVery_high() {
		return very_high;
	}

	/**
	 * Sets the very high.
	 *
	 * @param very_high the new very high
	 */
	public void setVery_high(Integer very_high) {
		this.very_high = very_high;
	}

	/**
	 * Gets the low.
	 *
	 * @return the low
	 */
	public Integer getLow() {
		return low;
	}

	/**
	 * Sets the low.
	 *
	 * @param low the new low
	 */
	public void setLow(Integer low) {
		this.low = low;
	}

	/**
	 * Gets the very low.
	 *
	 * @return the very low
	 */
	public Integer getVery_low() {
		return very_low;
	}

	/**
	 * Sets the very low.
	 *
	 * @param very_low the new very low
	 */
	public void setVery_low(Integer very_low) {
		this.very_low = very_low;
	}

	/**
	 * Gets the file name.
	 *
	 * @return the file name
	 */
	public String getFile_name() {
		return file_name;
	}

	/**
	 * Sets the file name.
	 *
	 * @param file_name the new file name
	 */
	public void setFile_name(String file_name) {
		this.file_name = file_name;
	}

	/**
	 * Gets the informational.
	 *
	 * @return the informational
	 */
	public Integer getInformational() {
		return informational;
	}

	/**
	 * Sets the informational.
	 *
	 * @param informational the new informational
	 */
	public void setInformational(Integer informational) {
		this.informational = informational;
	}

	/**
	 * Gets the medium.
	 *
	 * @return the medium
	 */
	public Integer getMedium() {
		return medium;
	}

	/**
	 * Sets the medium.
	 *
	 * @param medium the new medium
	 */
	public void setMedium(Integer medium) {
		this.medium = medium;
	}

	/**
	 * Gets the vulnerability summary.
	 *
	 * @return the vulnerability summary
	 */
	public Object getVulnerability_summary() {
		return vulnerability_summary;
	}

	/**
	 * Sets the vulnerability summary.
	 *
	 * @param vulnerability_summary the new vulnerability summary
	 */
	public void setVulnerability_summary(Object vulnerability_summary) {
		this.vulnerability_summary = vulnerability_summary;
	}

	/**
	 * Gets the version.
	 *
	 * @return the version
	 */
	public String getVersion() {
		return version;
	}

	/**
	 * Sets the version.
	 *
	 * @param version the new version
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * Gets the app count.
	 *
	 * @return the app count
	 */
	public Integer getApp_count() {
		return app_count;
	}

	/**
	 * Sets the app count.
	 *
	 * @param app_count the new app count
	 */
	public void setApp_count(Integer app_count) {
		this.app_count = app_count;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((account_id == null) ? 0 : account_id.hashCode());
		result = prime * result + ((app_count == null) ? 0 : app_count.hashCode());
		result = prime * result + ((component_id == null) ? 0 : component_id.hashCode());
		result = prime * result + ((file_name == null) ? 0 : file_name.hashCode());
		result = prime * result + ((high == null) ? 0 : high.hashCode());
		result = prime * result + ((informational == null) ? 0 : informational.hashCode());
		result = prime * result + ((low == null) ? 0 : low.hashCode());
		result = prime * result + ((medium == null) ? 0 : medium.hashCode());
		result = prime * result + ((version == null) ? 0 : version.hashCode());
		result = prime * result + ((very_high == null) ? 0 : very_high.hashCode());
		result = prime * result + ((very_low == null) ? 0 : very_low.hashCode());
		result = prime * result + ((vulnerability_summary == null) ? 0 : vulnerability_summary.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BlackListDTO other = (BlackListDTO) obj;
		if (account_id == null) {
			if (other.account_id != null)
				return false;
		} else if (!account_id.equals(other.account_id))
			return false;
		if (app_count == null) {
			if (other.app_count != null)
				return false;
		} else if (!app_count.equals(other.app_count))
			return false;
		if (component_id == null) {
			if (other.component_id != null)
				return false;
		} else if (!component_id.equals(other.component_id))
			return false;
		if (file_name == null) {
			if (other.file_name != null)
				return false;
		} else if (!file_name.equals(other.file_name))
			return false;
		if (high == null) {
			if (other.high != null)
				return false;
		} else if (!high.equals(other.high))
			return false;
		if (informational == null) {
			if (other.informational != null)
				return false;
		} else if (!informational.equals(other.informational))
			return false;
		if (low == null) {
			if (other.low != null)
				return false;
		} else if (!low.equals(other.low))
			return false;
		if (medium == null) {
			if (other.medium != null)
				return false;
		} else if (!medium.equals(other.medium))
			return false;
		if (version == null) {
			if (other.version != null)
				return false;
		} else if (!version.equals(other.version))
			return false;
		if (very_high == null) {
			if (other.very_high != null)
				return false;
		} else if (!very_high.equals(other.very_high))
			return false;
		if (very_low == null) {
			if (other.very_low != null)
				return false;
		} else if (!very_low.equals(other.very_low))
			return false;
		if (vulnerability_summary == null) {
			if (other.vulnerability_summary != null)
				return false;
		} else if (!vulnerability_summary.equals(other.vulnerability_summary))
			return false;
		return true;
	}
	
	
	
}
