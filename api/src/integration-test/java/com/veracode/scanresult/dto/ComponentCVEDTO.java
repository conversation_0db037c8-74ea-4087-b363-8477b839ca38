package com.veracode.scanresult.dto;

/**
 * The Class ComponentCVEDTO.
 */
public class ComponentCVEDTO {
	
	/** The cve score. */
	private Integer cve_score;
	
	/** The cve severity. */
	private String cve_severity;
	
	/** The cve id. */
	private String cve_id;
	
	/** The cwe id. */
	private String cwe_id;
	
	/** The cve summary. */
	private String cve_summary;
	
	/** The mitigated. */
	private Boolean mitigated;

	/**
	 * Gets the cve score.
	 *
	 * @return the cve score
	 */
	public Integer getCve_score() {
		return cve_score;
	}

	/**
	 * Sets the cve score.
	 *
	 * @param cve_score the new cve score
	 */
	public void setCve_score(Integer cve_score) {
		this.cve_score = cve_score;
	}

	/**
	 * Gets the cve severity.
	 *
	 * @return the cve severity
	 */
	public String getCve_severity() {
		return cve_severity;
	}

	/**
	 * Sets the cve severity.
	 *
	 * @param cve_severity the new cve severity
	 */
	public void setCve_severity(String cve_severity) {
		this.cve_severity = cve_severity;
	}

	/**
	 * Gets the cve id.
	 *
	 * @return the cve id
	 */
	public String getCve_id() {
		return cve_id;
	}

	/**
	 * Sets the cve id.
	 *
	 * @param cve_id the new cve id
	 */
	public void setCve_id(String cve_id) {
		this.cve_id = cve_id;
	}

	/**
	 * Gets the cwe id.
	 *
	 * @return the cwe id
	 */
	public String getCwe_id() {
		return cwe_id;
	}

	/**
	 * Sets the cwe id.
	 *
	 * @param cwe_id the new cwe id
	 */
	public void setCwe_id(String cwe_id) {
		this.cwe_id = cwe_id;
	}

	/**
	 * Gets the cve summary.
	 *
	 * @return the cve summary
	 */
	public String getCve_summary() {
		return cve_summary;
	}

	/**
	 * Sets the cve summary.
	 *
	 * @param cve_summary the new cve summary
	 */
	public void setCve_summary(String cve_summary) {
		this.cve_summary = cve_summary;
	}

	/**
	 * Gets the mitigated.
	 *
	 * @return the mitigated
	 */
	public Boolean getMitigated() {
		return mitigated;
	}

	/**
	 * Sets the mitigated.
	 *
	 * @param mitigated the new mitigated
	 */
	public void setMitigated(Boolean mitigated) {
		this.mitigated = mitigated;
	}
	
	
	
}
