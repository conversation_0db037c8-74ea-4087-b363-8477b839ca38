package com.veracode.scanresult.dto;

import java.util.List;

/**
 * The Class ComponentsDTO.
 */
public class ComponentsDTO {
	
		
		/** The blacklisted. */
		private Boolean blacklisted;
		
		/** The remediation text. */
		private String  remediation_text;
		
		/** The component vulnerabilities. */
		private List component_vulnerabilities;
		
		/** The file name. */
		private String file_name;
		
		/** The vulnerability summary. */
		private Object vulnerability_summary;
		
		/** The version. */
		private String version;
		
		/** The product name. */
		private String product_name;
		
		/** The links. */
		private Object _links;

		/**
		 * Gets the blacklisted.
		 *
		 * @return the blacklisted
		 */
		public Boolean getBlacklisted() {
			return blacklisted;
		}

		/**
		 * Sets the blacklisted.
		 *
		 * @param blacklisted the new blacklisted
		 */
		public void setBlacklisted(Boolean blacklisted) {
			this.blacklisted = blacklisted;
		}

		/**
		 * Gets the remediation text.
		 *
		 * @return the remediation text
		 */
		public String getRemediation_text() {
			return remediation_text;
		}

		/**
		 * Sets the remediation text.
		 *
		 * @param remediation_text the new remediation text
		 */
		public void setRemediation_text(String remediation_text) {
			this.remediation_text = remediation_text;
		}

		/**
		 * Gets the component vulnerabilities.
		 *
		 * @return the component vulnerabilities
		 */
		public List getComponent_vulnerabilities() {
			return component_vulnerabilities;
		}

		/**
		 * Sets the component vulnerabilities.
		 *
		 * @param component_vulnerabilities the new component vulnerabilities
		 */
		public void setComponent_vulnerabilities(List component_vulnerabilities) {
			this.component_vulnerabilities = component_vulnerabilities;
		}

		/**
		 * Gets the file name.
		 *
		 * @return the file name
		 */
		public String getFile_name() {
			return file_name;
		}

		/**
		 * Sets the file name.
		 *
		 * @param file_name the new file name
		 */
		public void setFile_name(String file_name) {
			this.file_name = file_name;
		}

		/**
		 * Gets the vulnerability summary.
		 *
		 * @return the vulnerability summary
		 */
		public Object getVulnerability_summary() {
			return vulnerability_summary;
		}

		/**
		 * Sets the vulnerability summary.
		 *
		 * @param vulnerability_summary the new vulnerability summary
		 */
		public void setVulnerability_summary(Object vulnerability_summary) {
			this.vulnerability_summary = vulnerability_summary;
		}

		/**
		 * Gets the version.
		 *
		 * @return the version
		 */
		public String getVersion() {
			return version;
		}

		/**
		 * Sets the version.
		 *
		 * @param version the new version
		 */
		public void setVersion(String version) {
			this.version = version;
		}

		/**
		 * Gets the product name.
		 *
		 * @return the product name
		 */
		public String getProduct_name() {
			return product_name;
		}

		/**
		 * Sets the product name.
		 *
		 * @param product_name the new product name
		 */
		public void setProduct_name(String product_name) {
			this.product_name = product_name;
		}

		/**
		 * Gets the links.
		 *
		 * @return the links
		 */
		public Object get_links() {
			return _links;
		}

		/**
		 * Sets the links.
		 *
		 * @param _links the new links
		 */
		public void set_links(Object _links) {
			this._links = _links;
		}
		
		
		
	

}
