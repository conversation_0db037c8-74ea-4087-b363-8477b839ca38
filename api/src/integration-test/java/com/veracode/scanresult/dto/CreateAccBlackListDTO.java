package com.veracode.scanresult.dto;

/**
 * The Class CreateAccBlackListDTO.
 */
public class CreateAccBlackListDTO {
	
	/** The account id. */
	private Integer account_id;
	
	/** The blacklisted. */
	private Boolean blacklisted;
	
	/** The component id. */
	private String component_id;
	
	/** The remediation text. */
	private String remediation_text;
	
	/** The state changed. */
	private Boolean state_changed;

	/**
	 * Gets the account id.
	 *
	 * @return the account id
	 */
	public Integer getAccount_id() {
		return account_id;
	}

	/**
	 * Sets the account id.
	 *
	 * @param account_id the new account id
	 */
	public void setAccount_id(Integer account_id) {
		this.account_id = account_id;
	}

	/**
	 * Gets the blacklisted.
	 *
	 * @return the blacklisted
	 */
	public Boolean getBlacklisted() {
		return blacklisted;
	}

	/**
	 * Sets the blacklisted.
	 *
	 * @param blacklisted the new blacklisted
	 */
	public void setBlacklisted(Boolean blacklisted) {
		this.blacklisted = blacklisted;
	}

	/**
	 * Gets the component id.
	 *
	 * @return the component id
	 */
	public String getComponent_id() {
		return component_id;
	}

	/**
	 * Sets the component id.
	 *
	 * @param component_id the new component id
	 */
	public void setComponent_id(String component_id) {
		this.component_id = component_id;
	}

	/**
	 * Gets the remediation text.
	 *
	 * @return the remediation text
	 */
	public String getRemediation_text() {
		return remediation_text;
	}

	/**
	 * Sets the remediation text.
	 *
	 * @param remediation_text the new remediation text
	 */
	public void setRemediation_text(String remediation_text) {
		this.remediation_text = remediation_text;
	}

	/**
	 * Gets the state changed.
	 *
	 * @return the state changed
	 */
	public Boolean getState_changed() {
		return state_changed;
	}

	/**
	 * Sets the state changed.
	 *
	 * @param state_changed the new state changed
	 */
	public void setState_changed(Boolean state_changed) {
		this.state_changed = state_changed;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((account_id == null) ? 0 : account_id.hashCode());
		result = prime * result + ((blacklisted == null) ? 0 : blacklisted.hashCode());
		result = prime * result + ((component_id == null) ? 0 : component_id.hashCode());
		result = prime * result + ((remediation_text == null) ? 0 : remediation_text.hashCode());
		result = prime * result + ((state_changed == null) ? 0 : state_changed.hashCode());
		return result;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CreateAccBlackListDTO other = (CreateAccBlackListDTO) obj;
		if (account_id == null) {
			if (other.account_id != null)
				return false;
		} else if (!account_id.equals(other.account_id))
			return false;
		if (blacklisted == null) {
			if (other.blacklisted != null)
				return false;
		} else if (!blacklisted.equals(other.blacklisted))
			return false;
		if (component_id == null) {
			if (other.component_id != null)
				return false;
		} else if (!component_id.equals(other.component_id))
			return false;
		if (remediation_text == null) {
			if (other.remediation_text != null)
				return false;
		} else if (!remediation_text.equals(other.remediation_text))
			return false;
		if (state_changed == null) {
			if (other.state_changed != null)
				return false;
		} else if (!state_changed.equals(other.state_changed))
			return false;
		return true;
	}
	
	

}
