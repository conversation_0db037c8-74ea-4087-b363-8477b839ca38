package com.veracode.scanresult.dto;

/**
 * The Class MitigationsDTO.
 */
public class MitigationsDTO {
	
	/** The mitigation id. */
	private String mitigation_id;
	
	/** The component id. */
	private String component_id;
	
	/** The cve id. */
	private String cve_id;
	
	/** The mitigation status. */
	private String mitigation_status;
	
	/** The mitigation reason. */
	private String mitigation_reason;
	
	/** The mitigation action. */
	private String mitigation_action;
	
	/** The comments. */
	private String comments;
	
	/** The deleted. */
	private Boolean deleted;
	
	/** The user name. */
	private String user_name;
	
	/** The user account name. */
	private String user_account_name;
	
	/** The user account id. */
	private Integer user_account_id;
	
	/** The mitigation status text. */
	private String mitigation_status_text;
	

	/**
	 * Gets the mitigation id.
	 *
	 * @return the mitigation id
	 */
	public String getMitigation_id() {
		return mitigation_id;
	}

	/**
	 * Sets the mitigation id.
	 *
	 * @param mitigation_id the new mitigation id
	 */
	public void setMitigation_id(String mitigation_id) {
		this.mitigation_id = mitigation_id;
	}

	/**
	 * Gets the component id.
	 *
	 * @return the component id
	 */
	public String getComponent_id() {
		return component_id;
	}

	/**
	 * Sets the component id.
	 *
	 * @param component_id the new component id
	 */
	public void setComponent_id(String component_id) {
		this.component_id = component_id;
	}

	/**
	 * Gets the cve id.
	 *
	 * @return the cve id
	 */
	public String getCve_id() {
		return cve_id;
	}

	/**
	 * Sets the cve id.
	 *
	 * @param cve_id the new cve id
	 */
	public void setCve_id(String cve_id) {
		this.cve_id = cve_id;
	}

	/**
	 * Gets the mitigation status.
	 *
	 * @return the mitigation status
	 */
	public String getMitigation_status() {
		return mitigation_status;
	}

	/**
	 * Sets the mitigation status.
	 *
	 * @param mitigation_status the new mitigation status
	 */
	public void setMitigation_status(String mitigation_status) {
		this.mitigation_status = mitigation_status;
	}

	/**
	 * Gets the mitigation reason.
	 *
	 * @return the mitigation reason
	 */
	public String getMitigation_reason() {
		return mitigation_reason;
	}

	/**
	 * Sets the mitigation reason.
	 *
	 * @param mitigation_reason the new mitigation reason
	 */
	public void setMitigation_reason(String mitigation_reason) {
		this.mitigation_reason = mitigation_reason;
	}

	/**
	 * Gets the mitigation action.
	 *
	 * @return the mitigation action
	 */
	public String getMitigation_action() {
		return mitigation_action;
	}

	/**
	 * Sets the mitigation action.
	 *
	 * @param mitigation_action the new mitigation action
	 */
	public void setMitigation_action(String mitigation_action) {
		this.mitigation_action = mitigation_action;
	}

	/**
	 * Gets the comments.
	 *
	 * @return the comments
	 */
	public String getComments() {
		return comments;
	}

	/**
	 * Sets the comments.
	 *
	 * @param comments the new comments
	 */
	public void setComments(String comments) {
		this.comments = comments;
	}

	/**
	 * Gets the deleted.
	 *
	 * @return the deleted
	 */
	public Boolean getDeleted() {
		return deleted;
	}

	/**
	 * Sets the deleted.
	 *
	 * @param deleted the new deleted
	 */
	public void setDeleted(Boolean deleted) {
		this.deleted = deleted;
	}

	/**
	 * Gets the user name.
	 *
	 * @return the user name
	 */
	public String getUser_name() {
		return user_name;
	}

	/**
	 * Sets the user name.
	 *
	 * @param user_name the new user name
	 */
	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}

	/**
	 * Gets the user account name.
	 *
	 * @return the user account name
	 */
	public String getUser_account_name() {
		return user_account_name;
	}

	/**
	 * Sets the user account name.
	 *
	 * @param user_account_name the new user account name
	 */
	public void setUser_account_name(String user_account_name) {
		this.user_account_name = user_account_name;
	}

	/**
	 * Gets the user account id.
	 *
	 * @return the user account id
	 */
	public Integer getUser_account_id() {
		return user_account_id;
	}

	/**
	 * Sets the user account id.
	 *
	 * @param user_account_id the new user account id
	 */
	public void setUser_account_id(Integer user_account_id) {
		this.user_account_id = user_account_id;
	}

	/**
	 * Gets the mitigation status text.
	 *
	 * @return the mitigation status text
	 */
	public String getMitigation_status_text() {
		return mitigation_status_text;
	}

	/**
	 * Sets the mitigation status text.
	 *
	 * @param mitigation_status_text the new mitigation status text
	 */
	public void setMitigation_status_text(String mitigation_status_text) {
		this.mitigation_status_text = mitigation_status_text;
	}
	
	
	

}