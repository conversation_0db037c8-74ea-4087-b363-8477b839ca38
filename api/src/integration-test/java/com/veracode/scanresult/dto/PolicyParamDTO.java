package com.veracode.scanresult.dto;

import java.util.List;

/**
 * The Class PolicyParamDTO.
 */
public class PolicyParamDTO {
	
	/** The policy name. */
	private String policy_name;
	
	/** The version. */
	private String version;
	
	/** The rules. */
	private List<PolicyRulesDTO> rules;
	
	/** The blacklist grace period days. */
	private Integer blacklist_grace_period_days;

	/**
	 * Gets the policy name.
	 *
	 * @return the policy name
	 */
	public String getPolicy_name() {
		return policy_name;
	}

	/**
	 * Sets the policy name.
	 *
	 * @param policy_name the new policy name
	 */
	public void setPolicy_name(String policy_name) {
		this.policy_name = policy_name;
	}

	/**
	 * Gets the version.
	 *
	 * @return the version
	 */
	public String getVersion() {
		return version;
	}

	/**
	 * Sets the version.
	 *
	 * @param version the new version
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * Gets the rules.
	 *
	 * @return the rules
	 */
	public List<PolicyRulesDTO> getRules() {
		return rules;
	}

	/**
	 * Sets the rules.
	 *
	 * @param rules the new rules
	 */
	public void setRules(List<PolicyRulesDTO> rules) {
		this.rules = rules;
	}

	/**
	 * Gets the blacklist grace period days.
	 *
	 * @return the blacklist grace period days
	 */
	public Integer getBlacklist_grace_period_days() {
		return blacklist_grace_period_days;
	}

	/**
	 * Sets the blacklist grace period days.
	 *
	 * @param blacklist_grace_period_days the new blacklist grace period days
	 */
	public void setBlacklist_grace_period_days(Integer blacklist_grace_period_days) {
		this.blacklist_grace_period_days = blacklist_grace_period_days;
	}
	
	
	

}
