package com.veracode.scanresult.dto;

/**
 * The Class VulnerabilityListDTO.
 */
public class VulnerabilityListDTO {
	
	/** The component id. */
	private String component_id;
	
	/** The cwe id. */
	private String cwe_id;
	
	/** The file name. */
	private String file_name;
	
	/** The mitigation status. */
	private String mitigation_status;
	
	/** The version. */
	private String version;
	
	/** The cwe category. */
	private String cwe_category;
	
	/** The mitigation reason. */
	private String mitigation_reason;
	
	/** The cve score. */
	private Integer cve_score;
	
	/** The cve severity. */
	private String cve_severity;
	
	/** The cve id. */
	private String cve_id;
	
	/** The mitigation status text. */
	private String mitigation_status_text;
	
	/** The cve summary. */
	private String cve_summary;
	
	/** The mitigated. */
	private Boolean mitigated;
	
	/**
	 * Gets the component id.
	 *
	 * @return the component id
	 */
	public String getComponent_id() {
		return component_id;
	}

	/**
	 * Sets the component id.
	 *
	 * @param component_id the new component id
	 */
	public void setComponent_id(String component_id) {
		this.component_id = component_id;
	}

	/**
	 * Gets the cwe id.
	 *
	 * @return the cwe id
	 */
	public String getCwe_id() {
		return cwe_id;
	}

	/**
	 * Sets the cwe id.
	 *
	 * @param cwe_id the new cwe id
	 */
	public void setCwe_id(String cwe_id) {
		this.cwe_id = cwe_id;
	}

	/**
	 * Gets the file name.
	 *
	 * @return the file name
	 */
	public String getFile_name() {
		return file_name;
	}

	/**
	 * Sets the file name.
	 *
	 * @param file_name the new file name
	 */
	public void setFile_name(String file_name) {
		this.file_name = file_name;
	}

	/**
	 * Gets the mitigation status.
	 *
	 * @return the mitigation status
	 */
	public String getMitigation_status() {
		return mitigation_status;
	}

	/**
	 * Sets the mitigation status.
	 *
	 * @param mitigation_status the new mitigation status
	 */
	public void setMitigation_status(String mitigation_status) {
		this.mitigation_status = mitigation_status;
	}

	/**
	 * Gets the version.
	 *
	 * @return the version
	 */
	public String getVersion() {
		return version;
	}

	/**
	 * Sets the version.
	 *
	 * @param version the new version
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * Gets the cwe category.
	 *
	 * @return the cwe category
	 */
	public String getCwe_category() {
		return cwe_category;
	}

	/**
	 * Sets the cwe category.
	 *
	 * @param cwe_category the new cwe category
	 */
	public void setCwe_category(String cwe_category) {
		this.cwe_category = cwe_category;
	}

	/**
	 * Gets the mitigation reason.
	 *
	 * @return the mitigation reason
	 */
	public String getMitigation_reason() {
		return mitigation_reason;
	}

	/**
	 * Sets the mitigation reason.
	 *
	 * @param mitigation_reason the new mitigation reason
	 */
	public void setMitigation_reason(String mitigation_reason) {
		this.mitigation_reason = mitigation_reason;
	}

	/**
	 * Gets the cve score.
	 *
	 * @return the cve score
	 */
	public Integer getCve_score() {
		return cve_score;
	}

	/**
	 * Sets the cve score.
	 *
	 * @param cve_score the new cve score
	 */
	public void setCve_score(Integer cve_score) {
		this.cve_score = cve_score;
	}

	/**
	 * Gets the cve severity.
	 *
	 * @return the cve severity
	 */
	public String getCve_severity() {
		return cve_severity;
	}

	/**
	 * Sets the cve severity.
	 *
	 * @param cve_severity the new cve severity
	 */
	public void setCve_severity(String cve_severity) {
		this.cve_severity = cve_severity;
	}

	/**
	 * Gets the cve id.
	 *
	 * @return the cve id
	 */
	public String getCve_id() {
		return cve_id;
	}

	/**
	 * Sets the cve id.
	 *
	 * @param cve_id the new cve id
	 */
	public void setCve_id(String cve_id) {
		this.cve_id = cve_id;
	}

	/**
	 * Gets the mitigation status text.
	 *
	 * @return the mitigation status text
	 */
	public String getMitigation_status_text() {
		return mitigation_status_text;
	}

	/**
	 * Sets the mitigation status text.
	 *
	 * @param mitigation_status_text the new mitigation status text
	 */
	public void setMitigation_status_text(String mitigation_status_text) {
		this.mitigation_status_text = mitigation_status_text;
	}

	/**
	 * Gets the cve summary.
	 *
	 * @return the cve summary
	 */
	public String getCve_summary() {
		return cve_summary;
	}

	/**
	 * Sets the cve summary.
	 *
	 * @param cve_summary the new cve summary
	 */
	public void setCve_summary(String cve_summary) {
		this.cve_summary = cve_summary;
	}

	/**
	 * Gets the mitigated.
	 *
	 * @return the mitigated
	 */
	public Boolean getMitigated() {
		return mitigated;
	}

	/**
	 * Sets the mitigated.
	 *
	 * @param mitigated the new mitigated
	 */
	public void setMitigated(Boolean mitigated) {
		this.mitigated = mitigated;
	}

	
	
	
	
	
}
