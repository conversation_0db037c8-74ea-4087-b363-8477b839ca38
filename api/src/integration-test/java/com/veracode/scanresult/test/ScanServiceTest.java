/*******************************************************************************
 * VERACODE.SOURCE.CONFIDENTIAL.agora-legacyscanservice.b5f7196e3c8d51cae90d11c2f37240654e19bcc09da964086d43ce67f1f200de
 * Copyright Veracode Inc., 2016
 *******************************************************************************/
package com.veracode.scanresult.test;

import static com.jayway.restassured.path.json.JsonPath.from;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.json.JSONObject;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.restassured.RestAssured;
import com.jayway.restassured.path.json.JsonPath;
import com.jayway.restassured.response.Response;
import com.veracode.scanresult.dto.BlackListDTO;
import com.veracode.scanresult.dto.ComponentCVEDTO;
import com.veracode.scanresult.dto.ComponentsDTO;
import com.veracode.scanresult.dto.CreateAccBlackListDTO;
import com.veracode.scanresult.dto.CreateMitigationsDTO;
import com.veracode.scanresult.dto.CreateMitigationsResponseDTO;
import com.veracode.scanresult.dto.MitigationListDTO;
import com.veracode.scanresult.dto.MitigationsDTO;
import com.veracode.scanresult.dto.PolicyParamDTO;
import com.veracode.scanresult.dto.PolicyRulesDTO;
import com.veracode.scanresult.dto.VulnerabilityListDTO;
import com.veracode.scanresult.util.AuthToken;
import com.veracode.scanresult.util.HTTPRetCodes;
import com.veracode.scanresult.util.RestApiClient;
import com.veracode.scanresult.util.ScaServiceTestConstants;
import com.veracode.scanresult.util.ServiceEndPoints;
import com.veracode.security.logging.SecureLogger;

@Test
public class ScanServiceTest {

	private static final String INTEGRATION = "integration";

	private static final String RESPONSE = "Response: ";

	private static final SecureLogger LOG = SecureLogger.getLogger(ScanServiceTest.class);

	protected RestApiClient restApiClient;

	private static List<String> PERMISSIONS = Arrays.asList("viewScaPortfolio", "manageScaBlacklist", "viewReports",
			"viewResultsInternal", "FEATURE_SCA_ENABLED");

	private static List<String> FEATURES = Arrays.asList("sca");

	private List<String> POLICY_PERMISSIONS = Arrays.asList("SCA-SCAPOLICY");

	AuthToken authToken = new AuthToken(ScaServiceTestConstants.USER_ID, ScaServiceTestConstants.ORGANIZATION_ID, 0,
			ScaServiceTestConstants.USER_NAME, ScaServiceTestConstants.TOKEN, true, false, PERMISSIONS, FEATURES, null);

	AuthToken policyAuthToken = new AuthToken(ScaServiceTestConstants.USER_ID, ScaServiceTestConstants.ORGANIZATION_ID,
			0, ScaServiceTestConstants.USER_NAME, ScaServiceTestConstants.TOKEN, true, false, POLICY_PERMISSIONS,
			FEATURES, null);

	@BeforeClass(alwaysRun = true)
	public void setUp() throws Exception {
		restApiClient = new RestApiClient(RestAssured.baseURI);

	}
	// BlackList Controller

	/**
	 * Test - Get blacklists based on accountId passed in
	 */
	@Test(groups = INTEGRATION)
	public void testAccBlackList() {

		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.ACCOUNT_BLACK_LIST_SUMMARY_ENDPOINT.endpoint()),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		JsonPath jsonPath = response.getBody().jsonPath();
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" Test Account Blacklist response : " + jsonResponse);
		BlackListDTO blackListDTO = jsonPath.getObject("_embedded.blacklist[0]", BlackListDTO.class);
		assertEquals(ScaServiceTestConstants.ACCOUNT_ID.toString(), blackListDTO.getAccount_id().toString());
		LOG.info("Test -Get blacklists based on accountId passed in success");
	}

	/*
	 * Create AccountBlacklist with the blacklist passed in
	 */
	@Test(groups = INTEGRATION)
	public void testCreateAccBlackList() {

		LOG.info(restApiClient.toString());
		CreateAccBlackListDTO createBlackListDTO = new CreateAccBlackListDTO();
		createBlackListDTO.setAccount_id(ScaServiceTestConstants.ACCOUNT_ID);
		createBlackListDTO.setComponent_id(ScaServiceTestConstants.COMPONENT_ID);
		createBlackListDTO.setBlacklisted(true);
		createBlackListDTO.setRemediation_text("Automation Remediated");
		createBlackListDTO.setState_changed(false);

		JSONObject jsonObject = new JSONObject(createBlackListDTO);
		final Response response = restApiClient.post(ServiceEndPoints.ACCOUNT_BLACK_LIST_SUMMARY_ENDPOINT.endpoint(),
				HTTPRetCodes.SUCCESS.getValue(), jsonObject.toString(), authToken);
		JsonPath jsonPath = response.getBody().jsonPath();
		CreateAccBlackListDTO responseDTO = jsonPath.getObject("", CreateAccBlackListDTO.class);
		assertTrue(responseDTO.equals(createBlackListDTO));
		LOG.info("Test - Create Account Black list with the blacklist passed in success");
	}

	// components controller
	/**
	 * Test - Get component details by component id
	 */
	@Test(groups = INTEGRATION)
	public void testGetComponentDetail() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(String.format(ServiceEndPoints.GET_COMPONENTS_ENDPOINT.endpoint(),
				ScaServiceTestConstants.COMPONENT_ID), HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" Test get component detail response  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		ComponentsDTO componentsDTO = jsonPath.getObject("", ComponentsDTO.class);
		assertTrue(componentsDTO.getProduct_name().equals("sca-test"));
		LOG.info("Test - Get component detail passed in success.");
	}

	/**
	 * Test - get dependent applications by component id
	 */
	@Test(groups = INTEGRATION)
	public void testGetDependentApplications() {

		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(String
				.format(ServiceEndPoints.GET_DEPENDENT_APP_ENDPOINT.endpoint(), ScaServiceTestConstants.COMPONENT_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetDependentApplications  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		assertHrefContainsResourceUrl(String.format(ServiceEndPoints.GET_DEPENDENT_APP_ENDPOINT.endpoint(),
				ScaServiceTestConstants.COMPONENT_ID), jsonPath);

	}

	/**
	 * Test - get component CVEs by component id
	 */
	@Test(groups = INTEGRATION)
	public void testGetCompnentCVEs() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(String
				.format(ServiceEndPoints.GET_COMPONENTS_CVE_ENDPOINT.endpoint(), ScaServiceTestConstants.COMPONENT_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		JsonPath jsonPath = response.getBody().jsonPath();
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" Test get component CVE response  : " + jsonResponse);
		ComponentCVEDTO componentCVEDTO = jsonPath.getObject("_embedded.vulnerabilities[0]", ComponentCVEDTO.class);
		assertTrue(componentCVEDTO.getCve_id().equals("CVE-0000-1001"));
	}

	/**
	 * Test - get component other versions by component id
	 */
	@Test(groups = INTEGRATION)
	public void testGetComponentOtherVersions() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient
				.get(String.format(ServiceEndPoints.GET_COMPONENTS_OTHER_VERSIONS_ENDPOINT.endpoint(),
						ScaServiceTestConstants.COMPONENT_ID), HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetComponentOtherVersions  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		assertHrefContainsResourceUrl(String.format(ServiceEndPoints.GET_COMPONENTS_OTHER_VERSIONS_ENDPOINT.endpoint(),
				ScaServiceTestConstants.COMPONENT_ID), jsonPath);

	}

	// InternalIntegration Controller

	/**
	 * Test - get scans
	 */
	@Test(groups = INTEGRATION)
	public void testGetScans() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCANS_ENDPOINT.endpoint(), ScaServiceTestConstants.APP_ID),
				HTTPRetCodes.SUCCESS.getValue());
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetScans  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		assertTrue(jsonPath.getString("_embedded.scans[0].scan_type").contains("SCA"));
	}

	/**
	 * Test - Retrieve a list of pointers to the findings carried out on the
	 * scan
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanFindings() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCAN_FINDINGS_ENDPOINT.endpoint(), ScaServiceTestConstants.SCAN_ID),
				HTTPRetCodes.SUCCESS.getValue());
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetScanFindings  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		assertHrefContainsResourceUrl(
				String.format(ServiceEndPoints.GET_SCAN_FINDINGS_ENDPOINT.endpoint(), ScaServiceTestConstants.SCAN_ID),
				jsonPath);

	}

	// ScanComponentController
	/**
	 * Get ScanComponent Detail for the scanId and component
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanComponentDetail() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCAN_COMPONENTS_ENDPOINT.endpoint(), ScaServiceTestConstants.SCAN_ID,
						ScaServiceTestConstants.COMPONENT_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetScanComponentDetail  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		assertTrue(jsonPath.getString("component_id").equals(ScaServiceTestConstants.COMPONENT_ID));

	}

	/**
	 * Test - Get scan vulnerabilities by scan id and component id
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanVulnerabilities() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCAN_COMPONENT_VUL_ENDPOINT.endpoint(),
						ScaServiceTestConstants.SCAN_ID, ScaServiceTestConstants.COMPONENT_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetScanVulnerabilities  : " + jsonResponse);
		VulnerabilityListDTO vulnerabilityListDto = jsonPath.getObject("_embedded.vulnerabilities[0]",
				VulnerabilityListDTO.class);
		assertEquals(ScaServiceTestConstants.COMPONENT_ID, vulnerabilityListDto.getComponent_id());
	}

	/**
	 * Test - get scan mitigations by scan id and component id
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanMitigations() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCAN_COMPONENT_MITIGATION_ENDPOINT.endpoint(),
						ScaServiceTestConstants.SCAN_ID, ScaServiceTestConstants.COMPONENT_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetScanMitigations  : " + jsonResponse);
		MitigationListDTO mitigationListDTO = jsonPath.getObject("_embedded.mitigations[0]", MitigationListDTO.class);
		assertEquals(ScaServiceTestConstants.COMPONENT_ID, mitigationListDTO.getComponent_id());

	}

	// ScanController
	/**
	 * Test - get scan components by scan id
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanComponentsByScanId() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(String
				.format(ServiceEndPoints.GET_COMPONENTS_BY_SCAN_ENDPOINT.endpoint(), ScaServiceTestConstants.SCAN_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetScanComponentsByScanId response  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		HashMap<String, String> jsonAsMap = from(response.asString()).get("_embedded.components[0]");
		assert (jsonAsMap.get("component_id") != null);
		assert (jsonAsMap.get("file_name") != null);

	}

	/**
	 * Test - get scan result by scan id
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanResult() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCAN_RESULT_ENDPOINT.endpoint(), ScaServiceTestConstants.SCAN_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		LOG.info(" testGetScanResult response  : " + jsonResponse);
		JsonPath jsonPath = response.getBody().jsonPath();
		assertEquals(jsonPath.getString("scan_id"), ScaServiceTestConstants.SCAN_ID);

	}

	/**
	 * Test - get scan vulnerability by scan id
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanVulnerability() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(String
				.format(ServiceEndPoints.GET_SCAN_VULNERABILITY_ENDPOINT.endpoint(), ScaServiceTestConstants.SCAN_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetScanVulnerability response  : " + jsonResponse);
		VulnerabilityListDTO vulnerabilityListDTO = jsonPath.getObject("_embedded.vulnerabilities[0]",
				VulnerabilityListDTO.class);

	}

	/**
	 * Test - get policy compliance by scan id
	 */
	@Test(groups = INTEGRATION)
	public void testGetScanPolicyCompilance() {
		LOG.info(restApiClient.toString());
		LOG.info("policy auth token :" +policyAuthToken.toString());
		LOG.info("auth Token : "+authToken.toString());
		String policyParam = buildPassedInPolicyString();
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_SCAN_POLICY_COMPILANCE_ENDPOINT.endpoint(),
						ScaServiceTestConstants.SCAN_ID),
				HTTPRetCodes.SUCCESS.getValue(), "policy", policyParam, policyAuthToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		assertEquals(jsonPath.getString("policy_name"),ScaServiceTestConstants.POLICY_NAME);
		LOG.info(" testGetScanPolicyCompilance response  : " + jsonResponse);

	}

	// PortfolioController
	/**
	 * l Test - get portfolio applications
	 */
	@Test(groups = INTEGRATION)
	public void testGetPortfolioApplications() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_PORTFOLIO_APPLICATIONS_ENDPOINT.endpoint()),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetPortfolioApplications response  : " + jsonResponse);
		assertHrefContainsResourceUrl(ServiceEndPoints.GET_PORTFOLIO_APPLICATIONS_ENDPOINT.endpoint(), jsonPath);

	}

	/**
	 * Test - get portfolio components
	 */
	@Test(groups = INTEGRATION)
	public void testGetPortfolioComponents() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_PORTFOLIO_COMPONENTS_ENDPOINT.endpoint()),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetPortfolioComponents response  : " + jsonResponse);
		assertHrefContainsResourceUrl(ServiceEndPoints.GET_PORTFOLIO_COMPONENTS_ENDPOINT.endpoint(), jsonPath);

	}

	/**
	 * Test - get portfolio vulnerabilities
	 */
	@Test(groups = INTEGRATION)
	public void testGetPortfolioVulnerabilities() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_PORTFOLIO_VULNERABILITY_ENDPOINT.endpoint()),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetPortfolioVulnerabilities response  : " + jsonResponse);
		assertHrefContainsResourceUrl(ServiceEndPoints.GET_PORTFOLIO_VULNERABILITY_ENDPOINT.endpoint(), jsonPath);

	}

	/**
	 * Test - get application result
	 *//*
	@Test(groups = INTEGRATION)
	public void testGetApplicationResult() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_APPLICATION_RESULT_ENDPOINT.endpoint()),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetApplicationResult response  : " + jsonResponse);
		assertHrefContainsResourceUrl(ServiceEndPoints.GET_APPLICATION_RESULT_ENDPOINT.endpoint(), jsonPath);

	}

	*//**
	 * Test - get application mitigations by app id
	 *//*
	@Test(groups = INTEGRATION)
	public void testGetApplicationMitigations() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(String
				.format(ServiceEndPoints.APPLICATION_MITIGATION_ENDPOINT.endpoint(), ScaServiceTestConstants.APP_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetApplicationMitigations response  : " + jsonResponse);
		assertTrue(jsonPath.getString("_embedded.mitigations[0].component_id")
				.equals("cccccccc-0000-0000-0000-000000000001"));

	}

	*//**
	 * Test - create application mitigations by app id
	 *//*
	@Test(groups = INTEGRATION)
	public void testCreateApplicationMitigation() {
		LOG.info(restApiClient.toString());
		CreateMitigationsDTO createMitigationDTO = new CreateMitigationsDTO();
		createMitigationDTO.setAction("COMMENT");
		createMitigationDTO.setComments("test create application mitigation");

		List<MitigationsDTO> mitigations = new ArrayList<>();
		MitigationsDTO mitigationDTO = new MitigationsDTO();
		mitigationDTO.setComments("test create application mitigation");
		mitigationDTO.setDeleted(true);
		mitigationDTO.setMitigation_id("mit001");
		mitigationDTO.setComponent_id(ScaServiceTestConstants.COMPONENT_ID);
		mitigationDTO.setCve_id("CVE-0000-1002");
		mitigationDTO.setMitigation_action("COMMENT");
		mitigationDTO.setMitigation_reason("NONE");
		mitigationDTO.setMitigation_status("NONE");
		mitigationDTO.setUser_account_id(1234);
		mitigationDTO.setUser_account_name("abc");
		mitigationDTO.setUser_name("abs");

		mitigations.add(mitigationDTO);

		createMitigationDTO.setMitigations(mitigations);

		JSONObject jsonObject = new JSONObject(createMitigationDTO);
		final Response response = restApiClient.post(
				String.format(ServiceEndPoints.APPLICATION_MITIGATION_ENDPOINT.endpoint(),
						Long.valueOf(ScaServiceTestConstants.APP_ID)),
				HTTPRetCodes.SUCCESS.getValue(), jsonObject.toString(), authToken);
		JsonPath jsonPath = response.getBody().jsonPath();
		CreateMitigationsResponseDTO responseDTO = jsonPath.getObject("", CreateMitigationsResponseDTO.class);
		assertTrue(responseDTO.getMitigations().get(0).getMitigation_id().equals("mit001"));
		LOG.info("Test - Create Application mitigation passed in success");

	}

	*//**
	 * Test - get application alerts by app id
	 *//*
	@Test(groups = INTEGRATION)
	public void testGetApplicationAlerts() {
		LOG.info(restApiClient.toString());
		final Response response = restApiClient.get(
				String.format(ServiceEndPoints.GET_APPLICATION_ALERTS.endpoint(), ScaServiceTestConstants.APP_ID),
				HTTPRetCodes.SUCCESS.getValue(), authToken);
		final JSONObject jsonResponse = new JSONObject(response.asString());
		JsonPath jsonPath = response.getBody().jsonPath();
		LOG.info(" testGetApplicationAlerts response  : " + jsonResponse);
		assertHrefContainsResourceUrl(
				String.format(ServiceEndPoints.GET_APPLICATION_ALERTS.endpoint(), ScaServiceTestConstants.APP_ID),
				jsonPath);

	}*/

	/**
	 * assert href contains the resource URL
	 * 
	 * @param resourceUrl
	 * @param jsonPath
	 */
	private void assertHrefContainsResourceUrl(String resourceUrl, JsonPath jsonPath) {
		String href = jsonPath.getString("_links.self.href");
		assert (href.contains(resourceUrl));
	}

	private String buildPassedInPolicyString() {

		PolicyParamDTO policyParamsDTO = new PolicyParamDTO();
		policyParamsDTO.setPolicy_name(ScaServiceTestConstants.POLICY_NAME);
		policyParamsDTO.setVersion("1");
		policyParamsDTO.setBlacklist_grace_period_days(10);

		PolicyRulesDTO scaSeverity = new PolicyRulesDTO();
		scaSeverity.setType("SCA_SEVERITY");
		scaSeverity.setReferenceId(Long.valueOf(5));

		PolicyRulesDTO scaBlackList = new PolicyRulesDTO();
		scaBlackList.setType("SCA_BLACKLIST");
		scaBlackList.setReferenceId(Long.valueOf(4));

		List<PolicyRulesDTO> rules = Arrays.asList(scaBlackList,scaSeverity);
		
		policyParamsDTO.setRules(rules);

		JSONObject jsonObject = new JSONObject(policyParamsDTO);
		return jsonObject.toString();
	}

}
