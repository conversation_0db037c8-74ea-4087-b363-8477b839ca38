/*******************************************************************************
 * VERACODE.SOURCE.CONFIDENTIAL.agora-legacyscanservice.b5f7196e3c8d51cae90d11c2f37240654e19bcc09da964086d43ce67f1f200de
 * Copyright Veracode Inc., 2016
 *******************************************************************************/
package com.veracode.scanresult.test;

import java.util.Locale;
import java.util.ResourceBundle;

import org.testng.annotations.AfterSuite;
import org.testng.annotations.BeforeSuite;

import com.jayway.restassured.RestAssured;
import com.jayway.restassured.response.Response;
import com.veracode.security.logging.SecureLogger;

/**
 * Service integration tests for config set up and tear down.
 */
public class SuiteConfigTest {

    private static final String HEALTH_ENDPOINT = "/health";
	private static final String SERVICE_HOST = "service.host";
	private static final String SERVICE_URL = "https://%s";
	private static final String SUITE_CONFIG_PROPERTIES_FILE = "suite";

    private static final SecureLogger LOG = SecureLogger.getLogger(SuiteConfigTest.class);

    /**
	 * Set up to be executed before each test.
	 */
    @BeforeSuite(alwaysRun = true)
    public void suiteSetUp() {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Executing suite set up...");
        }
        
		final ResourceBundle bundle = ResourceBundle.getBundle(SUITE_CONFIG_PROPERTIES_FILE, Locale.getDefault());
		RestAssured.baseURI = String.format(SERVICE_URL, bundle.getString(SERVICE_HOST));
		RestAssured.useRelaxedHTTPSValidation();
		RestAssured.enableLoggingOfRequestAndResponseIfValidationFails();
		
		if (LOG.isDebugEnabled()) {
		    LOG.debug("Base URI: " + RestAssured.baseURI);
		}
		
		final Response response = RestAssured.expect().statusCode(200).get(HEALTH_ENDPOINT);
		
		if (LOG.isDebugEnabled()) {
		    LOG.debug("Response: " + response.prettyPrint());
		}
	}

    /**
	 * Tear down to be executed after each test run.
	 */
	@AfterSuite(alwaysRun = true)
	public void suiteTearDown() {
	    if (LOG.isDebugEnabled()) {
	        LOG.debug("Executing suite tear down...");
	    }
	}
}
