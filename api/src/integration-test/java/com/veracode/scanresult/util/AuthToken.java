package com.veracode.scanresult.util;

import java.util.List;

/**
 * The Class AuthToken.
 */
public class AuthToken {
	
	  	/** The user id. */
	  	private int userId;
	    
    	/** The organization id. */
    	private int organizationId;
	    
    	/** The proxy organization id. */
    	private int proxyOrganizationId = 0;
	    
    	/** The username. */
    	private String username;
	    
    	/** The token. */
    	private String token = ScaServiceTestConstants.TOKEN;
	    
    	/** The internal. */
    	private boolean internal;
	    
    	/** The in maintenance mode. */
    	private boolean inMaintenanceMode;
	    
    	/** The permissions. */
    	private List permissions;
	    
    	/** The features. */
    	private List features;
	    
    	/** The authorities. */
    	private String authorities;
	    
		public AuthToken(){
			
		}
    	
    	public AuthToken(int userId, int organizationId, int proxyOrganizationId, String username, String token,
				boolean internal, boolean inMaintenanceMode, List permissions, List features, String authorities) {
			super();
			this.userId = userId;
			this.organizationId = organizationId;
			this.proxyOrganizationId = proxyOrganizationId;
			this.username = username;
			this.token = token;
			this.internal = internal;
			this.inMaintenanceMode = inMaintenanceMode;
			this.permissions = permissions;
			this.features = features;
			this.authorities = authorities;
		}

		/**
		 * Gets the user id.
		 *
		 * @return the user id
		 */
		public int getUserId() {
			return userId;
		}
		
		/**
		 * Sets the user id.
		 *
		 * @param userId the new user id
		 */
		public void setUserId(int userId) {
			this.userId = userId;
		}
		
		/**
		 * Gets the organization id.
		 *
		 * @return the organization id
		 */
		public int getOrganizationId() {
			return organizationId;
		}
		
		/**
		 * Sets the organization id.
		 *
		 * @param organizationId the new organization id
		 */
		public void setOrganizationId(int organizationId) {
			this.organizationId = organizationId;
		}
		
		/**
		 * Gets the proxy organization id.
		 *
		 * @return the proxy organization id
		 */
		public int getProxyOrganizationId() {
			return proxyOrganizationId;
		}
		
		/**
		 * Sets the proxy organization id.
		 *
		 * @param proxyOrganizationId the new proxy organization id
		 */
		public void setProxyOrganizationId(int proxyOrganizationId) {
			this.proxyOrganizationId = proxyOrganizationId;
		}
		
		/**
		 * Gets the username.
		 *
		 * @return the username
		 */
		public String getUsername() {
			return username;
		}
		
		/**
		 * Sets the username.
		 *
		 * @param username the new username
		 */
		public void setUsername(String username) {
			this.username = username;
		}
		
		/**
		 * Gets the token.
		 *
		 * @return the token
		 */
		public String getToken() {
			return token;
		}
		
		/**
		 * Sets the token.
		 *
		 * @param token the new token
		 */
		public void setToken(String token) {
			this.token = token;
		}
		
		/**
		 * Checks if is internal.
		 *
		 * @return true, if is internal
		 */
		public boolean isInternal() {
			return internal;
		}
		
		/**
		 * Sets the internal.
		 *
		 * @param internal the new internal
		 */
		public void setInternal(boolean internal) {
			this.internal = internal;
		}
		
		/**
		 * Checks if is in maintenance mode.
		 *
		 * @return true, if is in maintenance mode
		 */
		public boolean isInMaintenanceMode() {
			return inMaintenanceMode;
		}
		
		/**
		 * Sets the in maintenance mode.
		 *
		 * @param inMaintenanceMode the new in maintenance mode
		 */
		public void setInMaintenanceMode(boolean inMaintenanceMode) {
			this.inMaintenanceMode = inMaintenanceMode;
		}
		
		/**
		 * Gets the permissions.
		 *
		 * @return the permissions
		 */
		public List getPermissions() {
			return permissions;
		}
		
		/**
		 * Sets the permissions.
		 *
		 * @param permissions the new permissions
		 */
		public void setPermissions(List permissions) {
			this.permissions = permissions;
		}
		
		/**
		 * Gets the features.
		 *
		 * @return the features
		 */
		public List getFeatures() {
			return features;
		}
		
		/**
		 * Sets the features.
		 *
		 * @param features the new features
		 */
		public void setFeatures(List features) {
			this.features = features;
		}
		
		/**
		 * Gets the authorities.
		 *
		 * @return the authorities
		 */
		public String getAuthorities() {
			return authorities;
		}
		
		/**
		 * Sets the authorities.
		 *
		 * @param authorities the new authorities
		 */
		public void setAuthorities(String authorities) {
			this.authorities = authorities;
		}
	    
	    
	  

}
