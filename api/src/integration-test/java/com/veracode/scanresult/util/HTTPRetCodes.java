package com.veracode.scanresult.util;

/**
 * The Enum HTTPRetCodes.
 */
public enum HTTPRetCodes {
        
        /** The delete success. */
        DELETE_SUCCESS(204),
        
        /** The post success. */
        POST_SUCCESS(201),
        
        /** The success. */
        SUCCESS(200),
        
        /** The not authorized. */
        NOT_AUTHORIZED(401),
        
        /** The not found. */
        NOT_FOUND(404),
        
        /** The access denied. */
        ACCESS_DENIED(403),
        
        /** The bad request. */
        BAD_REQUEST(400),
        
        /** The server error. */
        SERVER_ERROR(500);

        /** The value. */
        private int value;

        /**
         * Instantiates a new HTTP ret codes.
         *
         * @param value the value
         */
        HTTPRetCodes(int value){
                this.value = value;
        }
        
        /**
         * Gets the value.
         *
         * @return the value
         */
        public int getValue() {
                return value;
        }

}