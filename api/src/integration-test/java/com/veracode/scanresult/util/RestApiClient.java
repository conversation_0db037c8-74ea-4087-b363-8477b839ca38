package com.veracode.scanresult.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.restassured.RestAssured;
import com.jayway.restassured.response.Response;
import com.veracode.security.logging.SecureLogger;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

import static com.jayway.restassured.RestAssured.given;

/**
 * The Class RestApiClient.
 */
public class RestApiClient {
    
    /** The m rest resources. */
    private String m_restResources;

    /** The Constant m_LOG. */
    private static final SecureLogger m_LOG = SecureLogger.getLogger(RestApiClient.class);

     /** The Constant X_AUTH_TOKEN. */
    private static final String X_AUTH_TOKEN = "X-AUTH-TOKEN";
    
    /** The Constant CONTENT_TYPE. */
    private static final String CONTENT_TYPE = "application/json";
     
    ObjectMapper mapper = new ObjectMapper();
    
    
    /**
     * Instantiates a new rest api client.
     *
     * @param baseURIWithPort the base URI with port
     */
    public RestApiClient(String baseURIWithPort) {
        RestAssured.baseURI = baseURIWithPort;
    }

    /**
     * Sets the rest resources.
     *
     * @param servicePrefix the service prefix
     * @param serviceVersion the service version
     * @param serviceResources the service resources
     */
    public void setRestResources(String servicePrefix, String serviceVersion, String serviceResources) {
        m_restResources = "/" + servicePrefix + "/" + serviceVersion + "/" + serviceResources;
    }

    /**
     * Gets the rest resources.
     *
     * @return the rest resources
     */
    public String getRestResources() {
        return m_restResources;
    }


    /**
     * Gets the.
     *
     * @param getUrl the get url
     * @param retCode the ret code
     * @return the response
     */
    public Response get(String getUrl, int retCode,AuthToken authToken) {
        String url = null;
        try {
            url = java.net.URLDecoder.decode(getUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            m_LOG.debug("Failed to decode the " + getUrl + " due to " + e.getLocalizedMessage());
        }

        return given().log().all().
                relaxedHTTPSValidation().
                baseUri(RestAssured.baseURI).
                contentType(CONTENT_TYPE).
                headers(X_AUTH_TOKEN,convertObjToJson(authToken, mapper)).
            when().
                get(url).
            then().
                statusCode(retCode).
                extract().
                response();
    }
    
    /**
     * Gets the with out authentication.
     *
     * @param getUrl the get url
     * @param retCode the ret code
     * @return the with out authentication
     */
    public Response get(String getUrl, int retCode) {
        String url = null;
        try {
            url = java.net.URLDecoder.decode(getUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            m_LOG.debug("Failed to decode the " + getUrl + " due to " + e.getLocalizedMessage());
        }

        return given().log().all().
                relaxedHTTPSValidation().
                baseUri(RestAssured.baseURI).
                contentType(CONTENT_TYPE).
            when().
                get(url).
            then().
                statusCode(retCode).
                extract().
                response();
    }
    
    /**
     * Post.
     *
     * @param postUrl the post url
     * @param retCode the ret code
     * @param jsonBody the json body
     * @return the response
     */
    public Response post(String postUrl, int retCode, String jsonBody,AuthToken authToken) {
    	
    	return
                given().log().all().
                    relaxedHTTPSValidation().
                    baseUri(RestAssured.baseURI).
                    contentType(CONTENT_TYPE).
                        headers(X_AUTH_TOKEN, convertObjToJson(authToken, mapper)).
                        body(jsonBody).
                when().
                    post(postUrl).
                then().
                    statusCode(retCode).
                    extract().
                    response();
		
	}
    
    /**
     * Gets the.
     *
     * @param getUrl the get url
     * @param retCode the ret code
     * @param queryParamName the query param name
     * @param queryParamValue the query param value
     * @return the response
     */
    public Response get(String getUrl, int retCode,String queryParamName,
    		String queryParamValue,AuthToken authToken) {
        String url = null;
        try {
            url = java.net.URLDecoder.decode(getUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            m_LOG.debug("Failed to decode the " + getUrl + " due to " + e.getLocalizedMessage());
        }

        return given().log().all().
                relaxedHTTPSValidation().
                baseUri(RestAssured.baseURI).
                contentType(CONTENT_TYPE).
                headers(X_AUTH_TOKEN,convertObjToJson(authToken, mapper)).queryParam(queryParamName, queryParamValue).
            when().
                get(url).
            then().
                statusCode(retCode).
                extract().
                response();
    }
    
    
    private static < T extends Object> String convertObjToJson( T obj, ObjectMapper mapper){
        String jsonString = null;
        try {
            jsonString = mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            m_LOG.debug("Failed to convert scanJob to json String due to: " + e.getLocalizedMessage());
        }
        //log the jsonstring.
        m_LOG.debug("jsonString - " + jsonString);
        return jsonString;
    }



}

