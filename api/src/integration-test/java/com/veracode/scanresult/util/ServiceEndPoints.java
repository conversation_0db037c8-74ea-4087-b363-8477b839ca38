package com.veracode.scanresult.util;

/**
 * The Enum ServiceEndPoints.
 */
public enum ServiceEndPoints {
	
	/** The scan components endpoint. */
	SCAN_COMPONENTS_ENDPOINT("/v1/blacklis"),
	
	/** The account black list summary endpoint. */
	ACCOUNT_BLACK_LIST_SUMMARY_ENDPOINT("/v1/blacklist"),
	
	/** The scans endpoint. */
	GET_SCANS_ENDPOINT("/internal/v1/applications/%s/scans"),
	
	/** The scan findings endpoint. */
	GET_SCAN_FINDINGS_ENDPOINT("/internal/v1/scans/%s/findings"),
	
	/** The components endpoint. */
	GET_COMPONENTS_ENDPOINT("/v1/components/%s"),
	
	/** The dependent app endpoint. */
	GET_DEPENDENT_APP_ENDPOINT("/v1/components/%s/applications"),
	
	/** The components cve endpoint. */
	GET_COMPONENTS_CVE_ENDPOINT("/v1/components/%s/vulnerabilities"),
	
	/** The components other versions endpoint. */
	GET_COMPONENTS_OTHER_VERSIONS_ENDPOINT("/v1/components/%s/otherversions"),
	
	/** The scan components endpoint. */
	GET_SCAN_COMPONENTS_ENDPOINT("/v1/scans/%s/components/%s"),
	
	/** The scan component vul endpoint. */
	GET_SCAN_COMPONENT_VUL_ENDPOINT("/v1/scans/%s/components/%s/vulnerabilities"),
	
	/** The scan component mitigation endpoint. */
	GET_SCAN_COMPONENT_MITIGATION_ENDPOINT("/v1/scans/%s/components/%s/mitigations"),
	
	/** The components by scan endpoint. */
	GET_COMPONENTS_BY_SCAN_ENDPOINT("/v1/scans/%s/components"),
	
	/** The scan result endpoint. */
	GET_SCAN_RESULT_ENDPOINT("/v1/scans/%s/result"),
	
	/** The scan vulnerability endpoint. */
	GET_SCAN_VULNERABILITY_ENDPOINT("/v1/scans/%s/vulnerabilities"),
	
	/** The scan policy compilance endpoint. */
	GET_SCAN_POLICY_COMPILANCE_ENDPOINT("/v1/scans/%s/policycompliance"),
	
	/** The portfolio applications endpoint. */
	GET_PORTFOLIO_APPLICATIONS_ENDPOINT("/v1/applications"),
	
	/** The portfolio components endpoint. */
	GET_PORTFOLIO_COMPONENTS_ENDPOINT("/v1/components"),
	
	/** The portfolio vulnerability endpoint. */
	GET_PORTFOLIO_VULNERABILITY_ENDPOINT("/v1/vulnerabilities"),
	
	/** The application result endpoint. */
	GET_APPLICATION_RESULT_ENDPOINT("/v1/applications/result"),
	
	/** The application mitigation endpoint. */
	APPLICATION_MITIGATION_ENDPOINT("/v1/applications/%s/mitigations"),
	
	/** The application alerts. */
	GET_APPLICATION_ALERTS("/v1/applications/%s/alerts");
	
	
	
	/** The end point. */
	private String endPoint;
	
	/**
	 * Instantiates a new service end points.
	 *
	 * @param endPoint the end point
	 */
	private ServiceEndPoints(String endPoint) {
		this.endPoint = endPoint;
	}
	
	/**
	 * Endpoint.
	 *
	 * @return the string
	 */
	public String endpoint(){
		return endPoint;
	}

}
