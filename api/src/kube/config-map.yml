apiVersion: v1
kind: ConfigMap
metadata:
  name: sca-scanresult-service-cm
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
data:
  ENVIRONMENT: ${ENVIRONMENT}
  MS_CONFIG_BUCKET: ${MS_CONFIG_BUCKET}
  DATABASE_URL_SCANRESULT: ${DATABASE_URL_SCANRESULT}
  KEY_STORE: ${KEY_STORE}
  TRUST_STORE: ${TRUST_STORE}
  ENABLE_SECRETS_MANAGER: ${ENABLE_SECRETS_MANAGER}
  CLIENTS_ALLOWED: ${CLIENTS_ALLOWED}
  SSL_CLIENT_AUTH: ${SSL_CLIENT_AUTH}
  RESULT_READY_Q_LISTENER_ENABLED: 'false'
  DATA_UPDATE_Q_LISTENER_ENABLED: 'false'
  PLATFORM_SIGN_KEY_FILE: ${PLATFORM_SIGN_KEY_FILE}
  REGISTRY_API_URL: ${REGISTRY_API_URL}
  PLATFORM_API_URL: ${PLATFORM_API_URL}
  JMS_ENABLED: 'false'
  DUMMY_DATA_ENABLED: ${DUMMY_DATA_ENABLED}
  APPLICATION_SERVICES_POLICY_BACKEND_URL: ${POLICY_BACKEND_URL}
  APPLICATION_JMS_SCAN_EVENT_ENABLED: ${APPLICATION_JMS_SCAN_EVENT_ENABLED}
  APPLICATION_JMS_SNS_ENABLED: ${APPLICATION_JMS_SNS_ENABLED}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sca-scanresult-processor-cm
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
data:
  ENVIRONMENT: ${ENVIRONMENT}
  MS_CONFIG_BUCKET: ${MS_CONFIG_BUCKET}
  DATABASE_URL_SCANRESULT: ${DATABASE_URL_SCANRESULT}
  KEY_STORE: ${KEY_STORE}
  TRUST_STORE: ${TRUST_STORE}
  ENABLE_SECRETS_MANAGER: ${ENABLE_SECRETS_MANAGER}
  CLIENTS_ALLOWED: ${CLIENTS_ALLOWED}
  SSL_CLIENT_AUTH: ${SSL_CLIENT_AUTH}
  RESULT_READY_Q_LISTENER_ENABLED: ${RESULT_READY_Q_LISTENER_ENABLED}
  DATA_UPDATE_Q_LISTENER_ENABLED: ${DATA_UPDATE_Q_LISTENER_ENABLED}
  PLATFORM_SIGN_KEY_FILE: ${PLATFORM_SIGN_KEY_FILE}
  REGISTRY_API_URL: ${REGISTRY_API_URL}
  PLATFORM_API_URL: ${PLATFORM_API_URL}
  JMS_ENABLED: 'true'
  APPLICATION_SERVICES_POLICY_BACKEND_URL: ${POLICY_BACKEND_URL}
  APPLICATION_JMS_SCAN_EVENT_ENABLED: ${APPLICATION_JMS_SCAN_EVENT_ENABLED}
  APPLICATION_JMS_SNS_ENABLED: ${APPLICATION_JMS_SNS_ENABLED}
  APPLICATION_SCHEDULED_JOB_ENABLED: 'true'