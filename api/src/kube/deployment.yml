apiVersion: apps/v1
kind: Deployment
metadata:
  name: sca-scanresult-service
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: sca-scanresult-service
  template:
    metadata:
      labels:
        app: sca-scanresult-service
        version: ${VERSION}
      annotations:
        iam.amazonaws.com/role: s3readonly
        prometheus.io/path: '/sca/scanresult/actuator/prometheus'
        prometheus.io/port: '8609'
        prometheus.io/scrape: 'true'
    spec:
      containers:
      - name: sca-scanresult-service
        image: ${POD_IMAGE}
        imagePullPolicy: Always
        ports:
        - name: https
          containerPort: 8509
          protocol: TCP
        - name: actuator
          containerPort: 8609
          protocol: TCP
        resources:
          requests:
            cpu: ${CONTAINER_RESOURCE_CPU}
            memory: ${CONTAINER_RESOURCE_MEMORY}
        env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        envFrom:
        - configMapRef:
            name: sca-scanresult-service-cm
        - secretRef:
            name: sca-scanresult-service-secret
        livenessProbe:
          httpGet:
            path: /sca/scanresult/actuator/health
            port: actuator
            scheme: HTTP
          initialDelaySeconds: 120
          periodSeconds: 3
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /sca/scanresult/actuator/health
            port: actuator
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 3
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sca-scanresult-processor
  labels:
    app: sca-scanresult-processor
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: sca-scanresult-processor
  template:
    metadata:
      labels:
        app: sca-scanresult-processor
        version: ${VERSION}
      annotations:
        iam.amazonaws.com/role: s3readonly
        prometheus.io/path: '/sca/scanresult/actuator/prometheus'
        prometheus.io/port: '8609'
        prometheus.io/scrape: 'true'
    spec:
      containers:
        - name: sca-scanresult-service
          image: ${POD_IMAGE}
          imagePullPolicy: Always
          ports:
            - name: https
              containerPort: 8509
              protocol: TCP
            - name: actuator
              containerPort: 8609
              protocol: TCP
          resources:
            requests:
              cpu: ${CONTAINER_RESOURCE_CPU}
              memory: ${CONTAINER_RESOURCE_MEMORY}
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - configMapRef:
                name: sca-scanresult-processor-cm
            - secretRef:
                name: sca-scanresult-service-secret
          livenessProbe:
            httpGet:
              path: /sca/scanresult/actuator/health
              port: actuator
              scheme: HTTP
            initialDelaySeconds: 120
            periodSeconds: 3
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /sca/scanresult/actuator/health
              port: actuator
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 3
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3

        