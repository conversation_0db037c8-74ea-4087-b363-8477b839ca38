apiVersion: v1
kind: Secret
metadata:
  name: sca-scanresult-service-secret
  labels:
    app: sca-scanresult-service
    version: 1.0.0
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
type: Opaque
data:
  DATABASE_USER: ${DATABASE_USER}
  DATABASE_PASSWORD: ${DATABASE_PASSWORD}
  KEY_STORE_PASSWORD: ${KEY_STORE_PASSWORD}
  TRUST_STORE_PASSWORD: ${TRUST_STORE_PASSWORD}