apiVersion: v1
kind: Service
metadata:
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
  name: sca-scanresult-service
spec:
  ports:
    - name: https
      port: 8509
      protocol: TCP
      targetPort: 8509
  selector:
    app: sca-scanresult-service
  type: ClusterIP

---

apiVersion: v1
kind: Service
metadata:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: scanresult.sca-prod.veracode.io
    service.beta.kubernetes.io/aws-load-balancer-internal: 0.0.0.0/0
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
  name: sca-scanresult-internal-service
spec:
  externalTrafficPolicy: Cluster
  ports:
    - name: https
      port: 443
      protocol: TCP
      targetPort: 8509
  selector:
    app: sca-scanresult-service
  type: LoadBalancer
