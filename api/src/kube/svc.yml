apiVersion: v1
kind: Service
metadata:
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
  name: sca-scanresult-service
spec:
  ports:
  - name: https
    port: 8509
    protocol: TCP
    targetPort: 8509
  selector:
    app: sca-scanresult-service
  type: ClusterIP

---

apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/backend-protocol: HTTPS
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/upstream-keepalive-timeout: "30"
  labels:
    app: sca-scanresult-service
    env: ${ENVIRONMENT}
    group: sca
    maintain: dl-sca-support
    email: dl-aws-sca
  name: sca-scanresult-service-ing
spec:
  rules:
    - host: scanresult-${KUBE_NAMESPACE}.sca-nonprod.veracode.io
      http:
        paths:
          - backend:
              serviceName: sca-scanresult-service
              servicePort: 8509
            path: /