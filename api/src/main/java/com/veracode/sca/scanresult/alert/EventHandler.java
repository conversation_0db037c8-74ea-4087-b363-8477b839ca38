package com.veracode.sca.scanresult.alert;

import com.veracode.sca.scanresult.alert.domain.Alert;
import com.veracode.sca.scanresult.alert.domain.Alert.AlertEntityType;
import com.veracode.sca.scanresult.alert.domain.Alert.AlertType;
import com.veracode.sca.scanresult.alert.domain.AppLinkEvent;
import com.veracode.sca.scanresult.alert.domain.ComponentCveEvent;
import com.veracode.sca.scanresult.alert.domain.CveEvent;
import com.veracode.sca.scanresult.alert.domain.ScanComponentEvent;
import com.veracode.sca.scanresult.alert.domain.ScanEvent;
import com.veracode.sca.scanresult.domain.AppProject;
import com.veracode.sca.scanresult.domain.ComponentCve;
import com.veracode.sca.scanresult.domain.ScaLatestScansPublished;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanStatus;
import com.veracode.sca.scanresult.integration.messaging.ScanMessageType;
import com.veracode.sca.scanresult.integration.messaging.ScanSQS;
import com.veracode.sca.scanresult.repo.AlertRepository;
import com.veracode.sca.scanresult.repo.ComponentCveRepository;
import com.veracode.sca.scanresult.repo.ScaLatestScansPublishedRepository;
import com.veracode.sca.scanresult.repo.ScanRepository;
import com.veracode.sca.scanresult.report.domain.LinkedScanComponent;
import com.veracode.sca.scanresult.report.repo.LinkedScanComponentRepository;
import com.veracode.security.logging.SecureLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Event handler for SCA scan events.
 */
@Component
public class EventHandler {

    public static final SecureLogger LOG = SecureLogger.getLogger(EventHandler.class);

    private final ComponentCveRepository componentCveRepository;
    private final LinkedScanComponentRepository linkedScanComponentRepository;
    private final ScaLatestScansPublishedRepository latestScansRepository;
    private final ScanRepository scanRepository;
    private final AlertRepository alertRepository;
    private final ScanSQS scanSQS;

    @Autowired
    public EventHandler(final ScaLatestScansPublishedRepository latestScansRepository, final LinkedScanComponentRepository linkedScanComponentRepository, final ComponentCveRepository componentCveRepository, final ScanRepository scanRepository,
            final AlertRepository alertRepository, final ScanSQS scanSQS) {
        this.latestScansRepository = latestScansRepository;
        this.linkedScanComponentRepository = linkedScanComponentRepository;
        this.componentCveRepository = componentCveRepository;
        this.scanRepository = scanRepository;
        this.alertRepository = alertRepository;
        this.scanSQS = scanSQS;
    }

    /**
     * Handle {@link ScanComponentEvent} events. Events are generated when new component is added into the scan.
     *
     * @param event instance of {@link ScanComponentEvent}.
     */
    @Async("scaEventExecutor")
    @EventListener
    public void handleScanEvent(final ScanEvent event) {

        if (event == null || StringUtils.isEmpty(event.getEntityId())) {
            return;
        }

        LOG.info("Alert : {}, entity (Scan) : {}", event.getAlertType(), event.getEntityId());

        switch (event.getAlertType()) {
            case SCAN_CREATED:
                scanSQS.save(event.getEntity(), ScanMessageType.CREATED);
                break;
            case SCAN_DELETED:
                scanSQS.save(event.getEntity(), ScanMessageType.DELETED);
                break;
            case SCAN_UNDELETED:
            case SCAN_COMPLETED:
                scanSQS.save(event.getEntity(), ScanMessageType.PUBLISHED);
                break;
            default:
                LOG.warn("Invalid alert type.");
        }
    }

    /**
     * Handle {@link ScanComponentEvent} events. Events are generated when new component is added into the scan.
     * 
     * @param event instance of {@link ScanComponentEvent}.
     */
    @Async("scaEventExecutor")
    @EventListener
    public void handleScanComponentEvent(final ScanComponentEvent event) {

        if (event == null || StringUtils.isEmpty(event.getEntityId())) {
            return;
        }
        LOG.info("Alert : {}, entity (ScanComponent) : {}", event.getAlertType(), event.getEntityId());

        if (AlertType.NEW_SCAN_COMPONENT.equals(event.getAlertType())) {
            final Optional<Scan> optionalScan = scanRepository.findById(event.getEntity().getScanId());
            optionalScan.ifPresent(
                scan -> alertRepository.save(Alert.init(scan.getAppId(), scan.getScanId(), AlertType.NEW_SCAN_COMPONENT,
                    AlertEntityType.SCAN_COMPONENT, event.getEntityId(), event.getAlertTs())));
        }
    }

    /**
     * Method to handle {@link ComponentCveEvent} events. These events are triggered when there is a new component cve
     * is created. If latest scans has this component, an alert will be created for the application.
     * 
     * @param event instance of {@link ComponentCveEvent}.
     */
    @Async("scaEventExecutor")
    @EventListener
    public void handleComponentCveEvent(final ComponentCveEvent event) {

        if (event == null || StringUtils.isEmpty(event.getEntityId())) {
            return;
        }
        LOG.info("Alert : {}, entity (ComponentCve) : {}", event.getAlertType(), event.getEntityId());

        if (AlertType.NEW_COMPONENT_CVE.equals(event.getAlertType()) && event.getEntity().getComponentId() != null) {
            final List<Scan> scans = scanRepository
                    .findLatestScansByComponentIdIn(Collections.singletonList(event.getEntity().getComponentId()));

            if (scans.isEmpty()) {
                scans.addAll(scanRepository
                        .findLatestProjectScansByComponentIdIn(Collections.singletonList(event.getEntity().getComponentId())));
            }

            scans.forEach(scan -> alertRepository.save(Alert.init(scan.getAppId(), scan.getScanId(),
                    AlertType.NEW_COMPONENT_CVE, AlertEntityType.COMPONENT_CVE, event.getEntityId(), event.getAlertTs())));
        }
    }

    /**
     * Method to handle {@link CveEvent} events. Event will be triggered when the cve score has being changed by the
     * source.
     * 
     * @param event instance of {@link CveEvent}.
     */
    @Async("scaEventExecutor")
    @EventListener
    public void handleCveEvent(final CveEvent event) {

        if (event == null || StringUtils.isEmpty(event.getEntityId())) {
            return;
        }
        LOG.info("Alert : {}, entity (Cve) : {}", event.getAlertType(), event.getEntityId());

        if (AlertType.CVE_SEVERITY_CHANGE.equals(event.getAlertType())) {

            final List<ComponentCve> componentCves =
                componentCveRepository.findByCveIdAndSuppressedFalse(event.getEntityId());

            if (componentCves != null && !componentCves.isEmpty()) {
                final List<Scan> scans = scanRepository.findLatestScansByComponentIdIn(
                        componentCves.stream().map(ComponentCve::getComponentId).collect(Collectors.toList()));

                if (scans.isEmpty()) {
                    scans.addAll(scanRepository.findLatestProjectScansByComponentIdIn(
                            componentCves.stream().map(ComponentCve::getComponentId).collect(Collectors.toList())));
                }

                final Set<String> uniqueScans = new HashSet<>();

                scans.forEach(scan -> {
                    if (!uniqueScans.contains(scan.getScanId())) {
                        alertRepository.save(Alert.init(scan.getAppId(), scan.getScanId(), AlertType.CVE_SEVERITY_CHANGE,
                                AlertEntityType.CVE, event.getEntityId(), event.getAlertTs()));
                        uniqueScans.add(scan.getScanId());
                    }
                });
            }
        }
    }

    /**
     * Method to handle {@link AppLinkEvent} events. Event will be triggered when a application is being linked with
     * project
     * @param event the AppLinkEvent
     */
    @Async("scaEventExecutor")
    @EventListener
    public void handleAppLinkEvent(final AppLinkEvent event) {

        if (Objects.isNull(event) || StringUtils.isEmpty(event.getEntityId())) {
            return;
        }

        if (AlertType.PROJECT_LINK.equals(event.getAlertType()) || AlertType.PROJECT_UNLINK.equals(
            event.getAlertType())) {
            LOG.info("Alert : {}, entity (AppProject) : {}", event.getAlertType(), event.getEntityId());

            final AppProject appProject = event.getEntity();
            final Scan latestScan = scanRepository.findFirstByAppIdAndStatusAndSandboxAndDeletedOrderByCompletedTsDesc(
                appProject.getAppId(), ScanStatus.COMPLETED, false, false);
            if (Objects.nonNull(latestScan)) {
                alertRepository.save(Alert.init(appProject.getAppId(), latestScan.getScanId(), event.getAlertType(),
                    AlertEntityType.APP_PROJECT, event.getEntityId(), event.getAlertTs()));
            } else {
                LOG.warn("Alert not saved for app :" + appProject.getAppId());
            }
        }
    }
}
