package com.veracode.sca.scanresult.alert;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * Configuration for event handler.
 */
@Configuration
public class EventHandlerConfig {

    @Value("${app.event-handler.core-pool-size:3}")
    private int corePoolSize;

    @Value("${app.event-handler.max-pool-size:3}")
    private int maxPoolSize;

    /**
     * Initialize scaEventExecutor to handle sca events.
     *
     * @return ThreadPoolTaskExecutor.
     */
    @Bean(name = "scaEventExecutor")
    public ThreadPoolTaskExecutor scaEventExecutor() {
        final ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setThreadNamePrefix("sca-event-handler-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        return executor;
    }
}
