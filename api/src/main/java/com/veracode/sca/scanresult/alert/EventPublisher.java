package com.veracode.sca.scanresult.alert;


import com.veracode.sca.scanresult.alert.domain.Alert;
import com.veracode.sca.scanresult.alert.domain.AppLinkEvent;
import com.veracode.sca.scanresult.alert.domain.ComponentCveEvent;
import com.veracode.sca.scanresult.alert.domain.CveEvent;
import com.veracode.sca.scanresult.alert.domain.ScanComponentEvent;
import com.veracode.sca.scanresult.alert.domain.ScanEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.veracode.sca.scanresult.domain.AppProject;
import com.veracode.sca.scanresult.domain.ComponentCve;
import com.veracode.sca.scanresult.domain.Cve;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanComponent;

import javax.annotation.Nonnull;

/**
 * Event publisher of scanner events.
 */
@Component
public class EventPublisher {

    final private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    public EventPublisher(final ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    /**
     * Trigger {@link ScanEvent} event, when scan is created (SCAN_CREATED)
     * 
     * @param scan event source {@link Scan}.
     */
    public void triggerScanCreated(final Scan scan) {
        applicationEventPublisher.publishEvent(new ScanEvent(scan, Alert.AlertType.SCAN_CREATED));
    }

    /**
     * Trigger {@link ScanEvent} when scan is deleted. (SCAN_DELETED).
     * 
     * @param scan event source {@link Scan}.
     */
    public void triggerScanDeleted(@Nonnull final Scan scan) {
        applicationEventPublisher.publishEvent(new ScanEvent(scan, Alert.AlertType.SCAN_DELETED));
    }

    /**
     * Trigger {@link ScanEvent} when scan is undeleted. (SCAN_UNDELETED).
     * 
     * @param scan event source {@link Scan}.
     */
    public void triggerScanUnDeleted(@Nonnull final Scan scan) {
        applicationEventPublisher.publishEvent(new ScanEvent(scan, Alert.AlertType.SCAN_UNDELETED));
    }

    /**
     * Trigger {@link ScanEvent} when scan is completed. (SCAN_COMPLETED).
     * 
     * @param scan event source {@link Scan}.
     */
    public void triggerScanCompleted(@Nonnull final Scan scan) {
        applicationEventPublisher.publishEvent(new ScanEvent(scan, Alert.AlertType.SCAN_COMPLETED));
    }

    /**
     * Trigger {@link ScanComponentEvent} event, when new component introduced to scan.
     * 
     * @param source source of the event, instance of {@link ScanComponent}.
     */
    public void triggerNewScanComponent(final ScanComponent source) {
        applicationEventPublisher.publishEvent(new ScanComponentEvent(source, Alert.AlertType.NEW_SCAN_COMPONENT));
    }

    /**
     * Trigger {@link ComponentCveEvent} event, when new cve introduced to a component.
     *
     * @param source source of the event, instance of {@link ComponentCve}.
     */
    public void triggerNewComponentCve(@Nonnull final ComponentCve source) {
        applicationEventPublisher.publishEvent(new ComponentCveEvent(source, Alert.AlertType.NEW_COMPONENT_CVE));
    }

    /**
     * Trigger {@link CveEvent} event, when severity of a cve get changed.
     *
     * @param source source of the event, instance of {@link Cve}.
     */
    public void triggerChangedCveSeverity(@Nonnull final Cve source) {
        applicationEventPublisher.publishEvent(new CveEvent(source, Alert.AlertType.CVE_SEVERITY_CHANGE));
    }


    /**
     * Trigger {@link CveEvent} event, when cve_id is added for existing srcclr_id
     *
     * @param source source of the event, instance of {@link Cve}.
     */
    public void triggerChangedSrcclrToCve(@Nonnull final Cve source) {
        applicationEventPublisher.publishEvent(new CveEvent(source, Alert.AlertType.CVE_ID_CHANGE));
    }

    /**
     * Trigger {@link AppLinkEvent} event with {@link Alert.AlertType}.PROJECT_LINK, when a application is linked
     * from a project.
     *
     * @param appProject instance of {@link AppProject}.
     */
    public void triggerApplink(@Nonnull final AppProject appProject) {
        applicationEventPublisher.publishEvent(new AppLinkEvent(appProject, Alert.AlertType.PROJECT_LINK));
    }

    /**
     * Trigger {@link AppLinkEvent} event with {@link Alert.AlertType}.PROJECT_UNLINK, when a application is un linked
     * from a project.
     *
     * @param appProject instance of {@link AppProject}.
     */
    public void triggerAppUnlink(@Nonnull final AppProject appProject) {
        applicationEventPublisher.publishEvent(new AppLinkEvent(appProject, Alert.AlertType.PROJECT_UNLINK));
    }
}
