package com.veracode.sca.scanresult.alert.domain;

import static com.veracode.sca.scanresult.Application.DATE_FORMAT;
import static com.veracode.sca.scanresult.Application.TIMEZONE;

import java.sql.Timestamp;
import java.util.List;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Transient;

import org.hibernate.annotations.GenericGenerator;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.veracode.sca.scanresult.policy.domain.PolicyComplianceStatus;
import com.veracode.sca.scanresult.util.TrackableEntity;

/**
 * JPA entity for Alert table. Class annotate with JPA {@link Entity} to indicate this is an entity class.
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.NONE, getterVisibility = JsonAutoDetect.Visibility.NONE,
    setterVisibility = JsonAutoDetect.Visibility.NONE)
@JsonPropertyOrder({"alert_id", "scan_id", "alert_type"})
@Entity
@SuppressWarnings({"PMD.DataClass"})
public class Alert extends TrackableEntity {

    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "uuid2")
    @Id
    private String alertId;

    private Long appId;

    private String scanId;

    @Enumerated(EnumType.STRING)
    private AlertType alertType;

    @Enumerated(EnumType.STRING)
    private AlertEntityType alertEntityType;

    private String alertEntityId;

    private Timestamp alertTs;

    private Boolean published;

    @Transient
    private PolicyComplianceStatus policyStatus;

    @Transient
    private List<AlertEntity> components;

    /**
     * Initialize method of {@link Alert}.
     *
     * @param appId unique identifier of application.
     * @param scanId unique identifier of scan.
     * @param alertType type, instance of {@link AlertType}.
     * @param alertEntityType entity type, instance of {@link AlertEntityType}.
     * @param alertEntityId unique identifier of entity.
     * @param alertTs alert ts.
     * @return alert instance.
     */
    public static Alert init(final Long appId, final String scanId, final AlertType alertType,
            final AlertEntityType alertEntityType, final String alertEntityId, final Timestamp alertTs) {
        final Alert alert = new Alert();
        alert.appId = appId;
        alert.scanId = scanId;
        alert.alertType = alertType;
        alert.alertEntityType = alertEntityType;
        alert.alertEntityId = alertEntityId;
        alert.alertTs = alertTs;
        return alert;
    }

    /**
     * Getter method of alertId.
     *
     * @return alert id.
     */
    @JsonProperty("alert_id")
    public String getId() {
        return alertId;
    }

    /**
     * Getter method of appId.
     *
     * @return app id.
     */
    public Long getAppId() {
        return appId;
    }

    /**
     * Getter method of scanId.
     *
     * @return scan id.
     */
    @JsonProperty
    public String getScanId() {
        return scanId;
    }

    /**
     * Getter method of alertType, instance of {@link AlertType}.
     *
     * @return alertType.
     */
    @JsonProperty
    public AlertType getAlertType() {
        return alertType;
    }

    /**
     * Getter method of alertEntityType, instance of enum {@link AlertEntityType}.
     *
     * @return alertEntityType.
     */
    public AlertEntityType getAlertEntityType() {
        return alertEntityType;
    }

    /**
     * Getter method of alertEntityId.
     *
     * @return alertEntityId.
     */
    public String getAlertEntityId() {
        return alertEntityId;
    }

    /**
     * Getter method of alertTs, which returns {@link Timestamp}.
     *
     * @return alertTs.
     */
    public Timestamp getAlertTs() {
        return alertTs;
    }

    /**
     * Getter method of insertTs, which returns {@link Timestamp}.
     *
     * @return insertTs.
     */
    @JsonProperty("alert_ts")
    @JsonFormat(pattern = DATE_FORMAT, timezone = TIMEZONE)
    @Override
    public Timestamp getInsertTs() {
        return super.getInsertTs();
    }

    /**
     * Getter method for policy status.
     *
     * @return policy status.
     */
    @JsonProperty
    public PolicyComplianceStatus getPolicyStatus() {
        return policyStatus;
    }

    /**
     * Setter method for policy status.
     *
     * @param policyStatus policy status.
     */
    public void setPolicyStatus(final PolicyComplianceStatus policyStatus) {
        this.policyStatus = policyStatus;
    }

    /**
     * Getter method for components.
     *
     * @return components.
     */
    @JsonProperty
    public List<AlertEntity> getComponents() {
        return components;
    }

    /**
     * Setter to set components.
     *
     * @param components components.
     */
    public void setComponents(final List<AlertEntity> components) {
        this.components = components;
    }

    @JsonProperty
    public Boolean getPublished() {
        return published;
    }

    public void setPublished(final Boolean published) {
        this.published = published;
    }

    /**
     * Enum containing the types of SCA Alerts.
     */
    public enum AlertType {

        SCAN_CREATED("Scan Created"),
        SCAN_DELETED("Scan deleted"),

        SCAN_UNDELETED("Scan Updated"),
        SCAN_COMPLETED("Scan Updated"),

        /**
         * New component has been introduced to the scan.
         */
        NEW_SCAN_COMPONENT("New component in scan"),

        /**
         * New CVE (vulnerability) has been introduced to existing component.
         */
        NEW_COMPONENT_CVE("New vulnerability in component"),

        /**
         * CVE (vulnerability) has been suppressed/unsuppressed to existing component.
         */
        //UPDATE_COMPONENT_CVE("Vulnerability in component updated"),

        /**
         * Severity of existing CVE (vulnerability) has been updated.
         */
        CVE_SEVERITY_CHANGE("Severity of the vulnerability changed"),

        /**
         * NVD cve_id is added for existing srcclr_id (No-CVE)
         */
        CVE_ID_CHANGE("NVD cve_id is added for existing srcclr_id"),

        /**
         * Cvss score of CVE (vulnerability) has been overridden by the account.
         */
        CVE_CVSS_OVERRIDE("Cvss Score of the vulnerability overridden"),

        /**
         * application link with a project
         */
        PROJECT_LINK("App link created"),

        /**
         * Application unlink with a project
         */
        PROJECT_UNLINK("App unlink");

        private String description;

        AlertType(final String description) {
            this.description = description;
        }

        /**
         * Getter method of description of {@link AlertType}.
         *
         * @return description.
         */
        public String getDescription() {
            return this.description;
        }
    }

    /**
     * Enum indicating the scanner alert entity type.
     */
    public enum AlertEntityType {

        /**
         * ScanComponent.ScanComponentId.
         */
        SCAN_COMPONENT,

        /**
         * ComponentCve.ComponentCveId.
         */
        COMPONENT_CVE,

        /**
         * Cve.CveId.
         */
        CVE,

        /**
         * AppProject.AppProjectId
         */
        APP_PROJECT
    }
}
