package com.veracode.sca.scanresult.alert.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Collections;
import java.util.List;

/**
 * Class to represent alert entity using component - cve.
 *
 */
public class AlertEntity {

    private String componentId;

    private String fileName;

    private Boolean violatePolicy;

    private List<AlertViolation> violations;

    /**
     * Initialize method of {@link AlertEntity}.
     *
     * @param componentId component id.
     * @param fileName filename of the component.
     * @param violatePolicy indicates violate policy.
     * @param violations list of cves.
     * @return instance of AlertEntity.
     */
    public static AlertEntity init(final String componentId, final String fileName, final Boolean violatePolicy,
                                   final List<AlertViolation> violations) {
        final AlertEntity alertEntity = new AlertEntity();
        alertEntity.componentId = componentId;
        alertEntity.fileName = fileName;
        alertEntity.violatePolicy = violatePolicy;
        alertEntity.violations = violations;
        return alertEntity;
    }

    /**
     * Initialize {@link AlertEntity}.
     *
     * @param componentId component id.
     * @param fileName filename
     * @param violatePolicy violatePolicy
     * @param violation violation.
     * @return instance of {@link AlertEntity}.
     */
    public static AlertEntity init(final String componentId, final String fileName, final Boolean violatePolicy,
                                   final AlertViolation violation) {
        return init(componentId, fileName, violatePolicy, Collections.singletonList(violation));
    }

    /**
     * Getter method for associated component id.
     *
     * @return componentId.
     */
    @JsonProperty
    public String getComponentId() {
        return componentId;
    }

    /**
     * Getter method of fileName.
     *
     * @return fileName.
     */
    @JsonProperty
    public String getFileName() {
        return fileName;
    }

    /**
     * Getter for violatePolicy.
     *
     * @return violatePolicy indicate alert violates the policy.
     */
    @JsonProperty
    public Boolean isViolatePolicy() {
        return violatePolicy;
    }

    /**
     * Setter for violatePolicy
     *
     * @param violatePolicy indicate violate policy
     */
    public void setViolatePolicy(final Boolean violatePolicy) {
        this.violatePolicy = violatePolicy;
    }

    /**
     * Getter for CVEs
     *
     * @return cves
     */
    @JsonProperty
    public List<AlertViolation> getViolations() {
        return violations;
    }
}
