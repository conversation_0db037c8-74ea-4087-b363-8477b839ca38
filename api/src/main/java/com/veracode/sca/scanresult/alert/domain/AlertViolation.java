package com.veracode.sca.scanresult.alert.domain;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.veracode.sca.scanresult.report.domain.AbstractViolation;
import com.veracode.sca.scanresult.report.domain.IssueSeverity;
import com.veracode.sca.scanresult.report.domain.LinkedScanVulnerability;
import com.veracode.sca.scanresult.report.domain.ScanVulnerability;

/**
 * Domain class for violations in SCA alerts.
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.NONE, getterVisibility = JsonAutoDetect.Visibility.NONE,
    setterVisibility = JsonAutoDetect.Visibility.NONE, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
public class AlertViolation extends AbstractViolation {

    private String cveId;

    private boolean mitigated;

    /**
     * Initialize method.
     * 
     * @param cveId cve id.
     * @param severity severity of the cve
     * @param mitigated mitigation status.
     * @param cveScore cve score.
     * @return instance of {@link AlertViolation}.
     */
    public static AlertViolation init(final String cveId, final IssueSeverity severity, final boolean mitigated,
            final float cveScore) {
        final AlertViolation violation = new AlertViolation();
        violation.cveId = cveId;
        violation.cveSeverity = severity;
        violation.mitigated = mitigated;
        violation.cveScore = cveScore;
        return violation;
    }

    /**
     * Initialize method.
     *
     * @param vulnerability instance of {@link ScanVulnerability}.
     * @return instance of {@link AlertViolation}.
     */
    public static AlertViolation init(final ScanVulnerability vulnerability) {
        return init(vulnerability.getCveId(), vulnerability.getCveSeverity(), vulnerability.isMitigated(),
            vulnerability.getCveScore());
    }

    /**
     * Initialize method.
     *
     * @param vulnerability instance of {@link LinkedScanVulnerability}.
     * @return instance of {@link AlertViolation}.
     */
    public static AlertViolation init(final LinkedScanVulnerability vulnerability) {
        return init(vulnerability.getCveId(), vulnerability.getCveSeverity(), vulnerability.isMitigated(),
                vulnerability.getCveScore());
    }

    /**
     * Return entity id (cveId).
     * 
     * @return cveId.
     */
    @Override
    public String getEntityId() {
        return cveId;
    }

    /**
     * Return cve id
     * 
     * @return cve id.
     */
    @JsonProperty
    @Override
    public String getCveId() {
        return cveId;
    }

    /**
     * return mitigation status.
     * 
     * @return mitigation status.
     */
    @JsonProperty
    @Override
    public boolean isMitigated() {
        return mitigated;
    }
}
