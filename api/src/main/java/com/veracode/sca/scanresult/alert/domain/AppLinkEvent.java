package com.veracode.sca.scanresult.alert.domain;

import com.veracode.sca.scanresult.domain.AppProject;

public class AppLinkEvent extends Event<AppProject> {
    /**
     * Create a new application link vent.
     *
     * @param entity    the object on which the event initially occurred (never {@code null}).
     * @param alertType enum AlertType.
     */
    public AppLinkEvent(AppProject entity, Alert.AlertType alertType) {
        super(entity, alertType);
    }

    @Override
    public String getEntityId() {
        return getEntity().getAppProjectId();
    }
}
