package com.veracode.sca.scanresult.alert.domain;

import com.veracode.sca.scanresult.domain.ComponentCve;

/**
 * Class to hold event related to component cve.
 */
public class ComponentCveEvent extends Event<ComponentCve> {

    /**
     * Create a new ComponentCveEvent.
     *
     * @param entity the object on which the event initially occurred (never {@code null})
     * @param alertType enum AlertType.
     */
    public ComponentCveEvent(final ComponentCve entity, final Alert.AlertType alertType) {
        super(entity, alertType);
        if (!Alert.AlertType.NEW_COMPONENT_CVE.equals(alertType)) {
            throw new IllegalArgumentException("Invalid Alert Type " + alertType + " for ComponentCve.");
        }
    }

    /**
     * Getter of entity id.
     * 
     * @return entity id.
     */
    @Override
    public String getEntityId() {
        return getEntity().getComponentCveId();
    }
}
