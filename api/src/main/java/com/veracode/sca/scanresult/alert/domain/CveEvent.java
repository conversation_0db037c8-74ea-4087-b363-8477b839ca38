package com.veracode.sca.scanresult.alert.domain;

import com.veracode.sca.scanresult.domain.Cve;

/**
 * Class to hold event related to cve.
 */
public class CveEvent extends Event<Cve> {

    private static final long serialVersionUID = 1L;

    /**
     * Create a new CveEvent.
     *
     * @param entity the object on which the event initially occurred (never {@code null}).
     * @param alertType enum AlertType.
     */
    public CveEvent(final Cve entity, final Alert.AlertType alertType) {
        super(entity, alertType);
        if (!(Alert.AlertType.CVE_SEVERITY_CHANGE.equals(alertType)
                || Alert.AlertType.CVE_ID_CHANGE.equals(alertType))) {
            throw new IllegalArgumentException("Invalid Alert Type " + alertType + " for Cve");
        }
    }

    /**
     * Getter of entity id.
     *
     * @return entity id.
     */
    @Override
    public String getEntityId() {
        return getEntity().getCveId();
    }
}
