package com.veracode.sca.scanresult.alert.domain;

import org.springframework.context.ApplicationEvent;

import java.sql.Timestamp;

/**
 * Abstract class to standardise the SCA scan events.
 *
 * @param <T> type of the event source.
 */
@SuppressWarnings("PMD.AbstractNaming")
public abstract class Event<T> extends ApplicationEvent {

    final private Alert.AlertType alertType;
    final private T entity;

    /**
     * Create a new ApplicationEvent.
     *
     * @param entity the object on which the event initially occurred (never {@code null}).
     * @param alertType enum AlertType.
     */
    public Event(final T entity, final Alert.AlertType alertType) {
        super(entity);
        this.entity = entity;
        this.alertType = alertType;
    }

    /**
     * Get the event source entity.
     *
     * @return event source entity.
     */
    public T getEntity() {
        return entity;
    }

    /**
     * Abstract method to enforce scan events to return entity id.
     * 
     * @return alert entity id.
     */
    public abstract Object getEntityId();

    /**
     * Get the {@link Alert.AlertType} of the event.
     *
     * @return alert type.
     */
    public Alert.AlertType getAlertType() {
        return this.alertType;
    }

    /**
     * Get timestamp of the event.
     *
     * @return timestamp of the event.
     */
    public Timestamp getAlertTs() {
        return getTimestamp() == 0 ? null : new Timestamp(getTimestamp());
    }
}
