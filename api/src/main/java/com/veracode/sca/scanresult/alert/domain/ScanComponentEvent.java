package com.veracode.sca.scanresult.alert.domain;

import com.veracode.sca.scanresult.domain.ScanComponent;

/**
 * Class to hold event related to Scan component.
 */
public class ScanComponentEvent extends Event<ScanComponent> {

    /**
     * Creates ScanComponentEvent.
     *
     * @param entity the object on which the event initially occurred (never {@code null}).
     * @param alertType enum AlertType.
     */
    public ScanComponentEvent(final ScanComponent entity, final Alert.AlertType alertType) {
        super(entity, alertType);
        if (!Alert.AlertType.NEW_SCAN_COMPONENT.equals(alertType)) {
            throw new IllegalArgumentException("Invalid Alert Type " + alertType + " for ScanComponent");
        }
    }

    /**
     * Getter of entity id.
     *
     * @return entity id.
     */
    @Override
    public String getEntityId() {
        return getEntity().getScanComponentId();
    }
}
