package com.veracode.sca.scanresult.alert.domain;

import com.veracode.sca.scanresult.domain.Scan;

public class ScanEvent extends Event<Scan> {

    public ScanEvent(Scan scan, Alert.AlertType alertType) {
        super(scan, alertType);
        if (!(Alert.AlertType.SCAN_CREATED.equals(alertType) || Alert.AlertType.SCAN_COMPLETED.equals(alertType)
                || Alert.AlertType.SCAN_DELETED.equals(alertType)
                || Alert.AlertType.SCAN_UNDELETED.equals(alertType))) {
            throw new IllegalArgumentException("Invalid Alert Type " + alertType + " for ScanComponent");
        }
    }

    @Override
    public String getEntityId() {
        return getEntity().getScanId();
    }
}
