package com.veracode.sca.scanresult.alert.service;

import java.util.List;
import java.util.stream.Collectors;

import com.veracode.sca.scanresult.alert.domain.Alert;
import com.veracode.sca.scanresult.alert.domain.AlertEntity;
import com.veracode.sca.scanresult.policy.PolicyComplianceModel;
import com.veracode.sca.scanresult.policy.domain.PolicyComplianceStatus;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.report.domain.ScanBlacklist;
import com.veracode.sca.scanresult.util.Violation;

/**
 * This class is to process {@link Alert}s against the application policy.
 */
public class AlertReportModel {

    private final List<String> blacklists;
    private List<Alert> alerts;

    /**
     * Constructor of alert report model.
     *
     * @param alerts application alerts.
     * @param blacklists blacklisted components.
     * @param policy policy
     */
    public AlertReportModel(final List<Alert> alerts, final List<ScanBlacklist> blacklists, final ScanPolicy policy) {
        this.blacklists = blacklists == null ? null
            : blacklists.stream().map(ScanBlacklist::getComponentId).collect(Collectors.toList());
        buildReponse(alerts, policy);
    }

    private boolean evaluateVulPolicy(final Violation vuln, final ScanPolicy policy) {
        if (policy == null) {
            return false;
        }
        final PolicyComplianceModel complianceModel = new PolicyComplianceModel(policy);
        complianceModel.processCveViolation(vuln);
        return PolicyComplianceStatus.DID_NOT_PASS.equals(complianceModel.getPolicyCompliance().getPolicyStatus());
    }

    private boolean evaluateBlacklistPolicy(final String componentId) {
        return blacklists != null && componentId != null && blacklists.contains(componentId);
    }

    private boolean evaluatePolicy(final AlertEntity entity, final ScanPolicy policy) {
        final boolean violatePolicy = evaluateBlacklistPolicy(entity.getComponentId())
                || entity.getViolations().stream().anyMatch(vuln -> evaluateVulPolicy(vuln, policy));
        entity.setViolatePolicy(violatePolicy);
        return violatePolicy;
    }

    private void buildReponse(final List<Alert> alerts, final ScanPolicy policy) {
        if (policy == null) {
            this.alerts =
                alerts.stream().filter(alert -> alert.getComponents() != null && !alert.getComponents().isEmpty())
                    .collect(Collectors.toList());
        } else {
            this.alerts = alerts.stream()
                .filter(alert -> alert.getComponents() != null && !alert.getComponents().isEmpty()).peek(alert -> {
                    PolicyComplianceStatus policyStatus = PolicyComplianceStatus.PASSED;
                    for (final AlertEntity entity : alert.getComponents()) {
                        policyStatus =
                            evaluatePolicy(entity, policy) ? PolicyComplianceStatus.DID_NOT_PASS : policyStatus;
                    }
                    alert.setPolicyStatus(policyStatus);
                }).collect(Collectors.toList());

        }
    }

    /**
     * Return evaluated list of {@link Alert}s.
     * 
     * @return List of {@link Alert}s.
     */
    public List<Alert> getReponse() {
        return this.alerts;
    }
}
