package com.veracode.sca.scanresult.alert.service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

import com.veracode.sca.scanresult.alert.domain.Alert;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.report.domain.CvssVersion;

/**
 * Interface to define operations on the application alerts. Application alerts are published by the sca scanner, based
 * on the pre-defined event types. Service will be invoked by the rest controllers and other parallel services.
 */
public interface AlertService {

    /**
     * Method returns list of {@link Alert}s which indicating which entities have been impacted by the event.
     *
     * @param appId unique identifier of application.
     * @param alertTs alerts will be retrieved from this time.
     * @param policy sca policy details.
     * @return list of {@link Alert}s.
     */
    List<Alert> getApplicationAlerts(long appId, Timestamp alertTs, ScanPolicy policy, CvssVersion cvssVersion);

    /**
     * Method returns a list of application ids which have the alerts not processed yet.
     *
     * @param batchSize the number of application ids to be retrieved.
     * @param alertTs alerts will be retrieved from this time.
     * @return a list of application ids.
     */
    List<BigInteger> getAlertedApplications(int batchSize, Timestamp alertTs);

    /**
     * Method to update the Alerts related to an application as processed.
     *
     * @param appId unique identifier of application.
     * @param insertTs time when alert to be modified.
     * @param status new status.
     * @return number of alerts updated.
     */
    int updateApplicationAlerts(long appId, Timestamp  insertTs, boolean status);

    /**
     * Method to update the Alerts related to applications as processed.
     *
     * @param appIds list of unique identifier of application.
     * @param status new status.
     * @return number of alerts updated.
     */
    int updateApplicationsAlerts(List<BigInteger> appIds, boolean status);

    /**
     * Method to update the Alerts related to applications as processed.
     *
     * @param appIds list of unique identifier of application.
     * @param status new status.
     * @return number of alerts updated.
     */
    int batchUpdateApplicationsAlerts(List<Long> appIds, boolean status);

    /**
     * Method returns a list of application ids and relevant insert ts which have the alerts not processed yet.
     *
     * @param batchSize the number of application ids to be retrieved.
     * @param alertTs alerts will be retrieved from this time.
     * @return a list of application ids.
     */
    List<Alert> getAlertedApplicationsWithoutAccessCheck(int batchSize, Timestamp alertTs);
}
