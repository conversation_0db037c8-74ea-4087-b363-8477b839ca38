package com.veracode.sca.scanresult.alert.service.impl;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.report.domain.LinkedScanVulnerability;
import com.veracode.sca.scanresult.report.repo.LinkedScanVulnerabilityCvss3Repository;
import com.veracode.sca.scanresult.report.repo.LinkedScanVulnerabilityRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import com.veracode.sca.scanresult.alert.domain.Alert;
import com.veracode.sca.scanresult.alert.domain.AlertEntity;
import com.veracode.sca.scanresult.alert.domain.AlertViolation;
import com.veracode.sca.scanresult.alert.service.AlertReportModel;
import com.veracode.sca.scanresult.alert.service.AlertService;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanComponent;
import com.veracode.sca.scanresult.domain.ScanStatus;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.repo.AlertRepository;
import com.veracode.sca.scanresult.repo.ScanComponentRepository;
import com.veracode.sca.scanresult.repo.ScanRepository;
import com.veracode.sca.scanresult.report.domain.ComponentCve;
import com.veracode.sca.scanresult.report.domain.ScanBlacklist;
import com.veracode.sca.scanresult.report.repo.ComponentCveRepository;
import com.veracode.sca.scanresult.report.repo.ScanBlacklistRepository;


/**
 * Implementation class of {@link AlertService}.
 */
@SuppressWarnings("PMD.CyclomaticComplexity")
@Service
public class AlertServiceImpl implements AlertService {

    private final AlertRepository alertRepo;

    private final ScanRepository scanRepo;

    private final ComponentCveRepository compCveRepo;

    private final ScanComponentRepository scanCompRepo;

    private final LinkedScanVulnerabilityRepository linkedScanVulnRepo;

    private final LinkedScanVulnerabilityCvss3Repository linkedScanVulnCvss3Repo;

    private final ScanBlacklistRepository scanBlacklistRepo;

    /**
     * Public constructor.
     *
     * @param alertRepo {@link AlertRepository}.
     * @param scanRepo {@link ScanRepository}.
     * @param compCveRepo {@link ComponentCveRepository}.
     * @param scanCompRepo {@link ScanComponentRepository}.
     * @param linkedScanVulnRepo {@link LinkedScanVulnerabilityRepository}.
     * @param scanBlacklistRepo {@link ScanBlacklistRepository}.
     * @param linkedScanVulnCvss3Repo {@link LinkedScanVulnerabilityCvss3Repository}.
     */
    @Autowired
    public AlertServiceImpl(final AlertRepository alertRepo, final ScanRepository scanRepo,
                            @Qualifier("component_cve_vw_repo") final ComponentCveRepository compCveRepo, final ScanComponentRepository scanCompRepo,
                            final LinkedScanVulnerabilityRepository linkedScanVulnRepo, final ScanBlacklistRepository scanBlacklistRepo,
                            final LinkedScanVulnerabilityCvss3Repository linkedScanVulnCvss3Repo) {
        this.alertRepo = alertRepo;
        this.scanRepo = scanRepo;
        this.compCveRepo = compCveRepo;
        this.scanCompRepo = scanCompRepo;
        this.linkedScanVulnRepo = linkedScanVulnRepo;
        this.scanBlacklistRepo = scanBlacklistRepo;
        this.linkedScanVulnCvss3Repo = linkedScanVulnCvss3Repo;
    }

    /**
     * {@inheritDoc}.
     */
    @SuppressWarnings({"PMD.CommentRequired", "PMD.NPathComplexity"})
    @Override
    @PreAuthorize("hasAnyAuthority('viewReports', 'viewResults', 'SCA-NOTIFICATION') && @authorizeApplication.canAccess(principal,#appId,#policy)")
    public List<Alert> getApplicationAlerts(final long appId, final Timestamp alertTs, final ScanPolicy policy, final CvssVersion cvssVersion) {

            final Scan latestScan =
                scanRepo.findFirstByAppIdAndStatusOrderByCompletedTsDesc(appId, ScanStatus.COMPLETED);

            if (latestScan == null) {
                return new ArrayList<>();
            }

            final List<String> latestScanCompIds = getLatestScanComponentIds(latestScan.getScanId());

            if(latestScanCompIds == null || latestScanCompIds.isEmpty()) {
                return new ArrayList<>();
            }

            final List<Alert> alerts = alertTs == null ? alertRepo.findByAppId(appId)
                : alertRepo.findAlertsForApplicationFromInsertTs(appId, alertTs);

            if (alerts == null || alerts.isEmpty()) {
                return new ArrayList<>();
            }

            alerts.forEach(alert -> {
                final List<AlertEntity> entities = getEntities(alert, cvssVersion);
                filterWithLatestScan(entities, latestScanCompIds);
                alert.setComponents(entities);
            });

            List<ScanBlacklist> blacklists = null;
            if (policy != null && policy.getBlacklistRule() != null) {
                blacklists =
                    scanBlacklistRepo.findByScanId(latestScan.getScanId(), policy.getAccountId());
            }

            final AlertReportModel reportModel = new AlertReportModel(alerts, blacklists, policy);
            return reportModel.getReponse();
    }

    /**
     * {@inheritDoc}.
     */
    @SuppressWarnings({"PMD.CommentRequired", "PMD.NPathComplexity"})
    @Override
    @PreAuthorize("hasAnyAuthority('viewReports', 'viewResults', 'SCA-NOTIFICATION')")
    public List<BigInteger> getAlertedApplications(int batchSize, Timestamp alertTs) {
        return alertRepo.findTopApplicationsAfterInsertTs(batchSize, alertTs);
    }

    /**
     * {@inheritDoc}.
     */
    @SuppressWarnings({"PMD.CommentRequired", "PMD.NPathComplexity"})
    @Override
    public List<Alert> getAlertedApplicationsWithoutAccessCheck(int batchSize, Timestamp alertTs) {
        return alertRepo.findTopApplicationsFromInsertTs(batchSize, alertTs);
    }

    /**
     * {@inheritDoc}.
     */
    @SuppressWarnings({"PMD.CommentRequired", "PMD.NPathComplexity"})
    @Override
    @PreAuthorize("hasAnyAuthority('viewReports', 'viewResults', 'SCA-NOTIFICATION')")
    public int updateApplicationAlerts(long appId, Timestamp  insertTs, boolean status) {
        return alertRepo.updateAlertStatus(appId, insertTs, status);
    }

    /**
     * {@inheritDoc}.
     */
    @SuppressWarnings({"PMD.CommentRequired", "PMD.NPathComplexity"})
    @Override
    @PreAuthorize("hasAnyAuthority('viewReports', 'viewResults', 'SCA-NOTIFICATION')")
    public int updateApplicationsAlerts(List<BigInteger> appIds, boolean status) {
        return alertRepo.updateAlertsStatus(appIds, status);
    }

    /**
     * {@inheritDoc}.
     */
    @Override
    public int batchUpdateApplicationsAlerts(List<Long> appIds, boolean status) {
        return alertRepo.batchUpdateAlertStatus(appIds, status);
    }

    private void filterWithLatestScan(final List<AlertEntity> alertEntities, final List<String> latestScanComponents) {
        if (alertEntities != null) {
            alertEntities.removeIf(entity -> !latestScanComponents.contains(entity.getComponentId()));
        }
    }

    private List<String> getLatestScanComponentIds(final String latestScanId) {
        final List<ScanComponent> latestScanComponents = scanCompRepo.findByScanId(latestScanId);
        return latestScanComponents.stream().map(ScanComponent::getComponentId).collect(Collectors.toList());
    }

    /**
     * Get alert entities mapped to the alert.
     *
     * @param alert instance of {@link Alert}.
     *
     * @return list of AlertEntities.
     */
    private List<AlertEntity> getEntities(final Alert alert, final CvssVersion cvssVersion) {
        if (Alert.AlertEntityType.SCAN_COMPONENT.equals(alert.getAlertEntityType())) {
            return getAlertEntitiesByScanComponent(alert.getScanId(), alert.getAlertEntityId(), cvssVersion);
        } else if (Alert.AlertEntityType.COMPONENT_CVE.equals(alert.getAlertEntityType())) {
            return getAlertEntitiesByComponentCve(alert.getScanId(), alert.getAlertEntityId(), cvssVersion);
        } else if (Alert.AlertEntityType.CVE.equals(alert.getAlertEntityType())) {
            return getAlertEntitiesByCve(alert.getScanId(), alert.getAlertEntityId(), cvssVersion);
        }
        return null;
    }

    /**
     * Retrieve alert entities based on {@link ScanComponent}.scanComponentId.
     *
     * @param scanId scan id.
     * @param scanComponentId scanComponentId.
     * @return List of {@link AlertEntity}s.
     */
    private List<AlertEntity> getAlertEntitiesByScanComponent(final String scanId, final String scanComponentId, final CvssVersion cvssVersion) {
        final ScanComponent scanComponent = scanCompRepo.findById(scanComponentId).orElse(null);
        if (scanComponent == null) {
            return null;
        }
        List<LinkedScanVulnerability> vulnerabilities;
        if (CvssVersion.CVSS_VERSION_3.equals(cvssVersion)) {
            vulnerabilities = getLinkedScanVulnByScanComponentCvss3(scanId, scanComponent.getComponentId());
        } else {
            vulnerabilities = getLinkedScanVulnByScanComponent(scanId, scanComponent.getComponentId());
        }

        final List<AlertViolation> violations =
            vulnerabilities.stream().map(AlertViolation::init).collect(Collectors.toList());
        return new ArrayList<>(Arrays
            .asList(AlertEntity.init(scanComponent.getComponentId(), scanComponent.getFileName(), null, violations)));
    }

    /**
     * Retrieve alert entities based on {@link ComponentCve}.componentCveId.
     * 
     * @param scanId scanId
     * @param componentCveId componentCveId.
     * @return List of {@link AlertEntity}s.
     */
    private List<AlertEntity> getAlertEntitiesByComponentCve(final String scanId, final String componentCveId, final CvssVersion cvssVersion) {
        final ComponentCve componentCve = compCveRepo.findByComponentCveId(componentCveId);
        if (componentCve == null) {
            return null;
        }
        LinkedScanVulnerability vulnerability;
        if (CvssVersion.CVSS_VERSION_3.equals(cvssVersion)) {
            vulnerability = getLinkedScanVulnByComponentCveCvss3(scanId, componentCve.getComponentId(), componentCve.getCveId());
        } else {
            vulnerability = getLinkedScanVulnByComponentCve(scanId, componentCve.getComponentId(), componentCve.getCveId());
        }

        if (vulnerability == null) {
            return null;
        }

        return new ArrayList<>(Arrays.asList(AlertEntity.init(componentCve.getComponentId(),
            vulnerability.getFileName(), null, AlertViolation.init(vulnerability))));
    }

    /**
     * Retrieve alert entities based on cveId.
     * 
     * @param scanId scanId
     * @param cveId cveId
     * @return List of {@link AlertEntity}s.
     */
    private List<AlertEntity> getAlertEntitiesByCve(final String scanId, final String cveId, final CvssVersion cvssVersion) {
        List<LinkedScanVulnerability> vulnerabilities;

        if (CvssVersion.CVSS_VERSION_3.equals(cvssVersion)) {
            vulnerabilities = getLinkedScanVulnByCveCvss3(scanId, cveId);
        } else {
            vulnerabilities = getLinkedScanVulnByCve(scanId, cveId);
        }
        return vulnerabilities.stream()
                .map(vuln -> AlertEntity.init(vuln.getComponentId(), vuln.getFileName(), null, AlertViolation.init(vuln)))
                .collect(Collectors.toList());
    }

    private List<LinkedScanVulnerability> getLinkedScanVulnByScanComponent(final String scanId, final String componentId) {
        return linkedScanVulnRepo.findByIdScanIdAndIdComponentId(scanId, componentId);
    }

    private List<LinkedScanVulnerability> getLinkedScanVulnByScanComponentCvss3(final String scanId, final String componentId) {
            return linkedScanVulnCvss3Repo.findByIdScanIdAndIdComponentId(scanId, componentId).stream().map(LinkedScanVulnerability::convert).collect(Collectors.toList());
    }

    private LinkedScanVulnerability getLinkedScanVulnByComponentCve(final String scanId, final String componentId, final String cveId) {
        return linkedScanVulnRepo.findByIdScanIdAndIdComponentIdAndIdCveId(scanId, componentId, cveId);
    }

    private LinkedScanVulnerability getLinkedScanVulnByComponentCveCvss3(final String scanId, final String componentId, final String cveId) {
        return LinkedScanVulnerability.convert(linkedScanVulnCvss3Repo.findByIdScanIdAndIdComponentIdAndIdCveId(scanId, componentId, cveId));
    }

    private List<LinkedScanVulnerability> getLinkedScanVulnByCve(final String scanId, final String cveId) {
        return linkedScanVulnRepo.findByIdScanIdAndIdCveId(scanId, cveId);
    }

    private List<LinkedScanVulnerability> getLinkedScanVulnByCveCvss3(final String scanId, final String cveId) {
        return linkedScanVulnCvss3Repo.findByIdScanIdAndIdCveId(scanId, cveId).stream().map(LinkedScanVulnerability::convert).collect(Collectors.toList());
    }
}
