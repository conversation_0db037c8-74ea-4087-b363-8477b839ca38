package com.veracode.sca.scanresult.authz;

import java.util.List;

import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.veracode.agora.identity.auth.Principal;
import com.veracode.sca.scanresult.domain.MitigationAction;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanStatus;
import com.veracode.sca.scanresult.domain.ShareScan;
import com.veracode.sca.scanresult.repo.ScanRepository;
import com.veracode.sca.scanresult.repo.ShareScanRepository;
import com.veracode.sca.scanresult.util.UserPrincipal;
import com.veracode.security.logging.SecureLogger;

/**
 * 
 * Authorization of a user to access application information
 *
 */
@Service("authorizeApplication")
public class AuthorizeApplication {

    private static final SecureLogger LOG = SecureLogger.getLogger(AuthorizeApplication.class);
    private static final String TYPE = "application";

    private final ScanRepository scanRepo;
    private final ShareScanRepository shareScanRepo;

    /**
     * Authorize Application
     * 
     * @param scanRepo the scanRepo
     * @param shareScanRepo the shareScanRepo
     */
    @Autowired
    public AuthorizeApplication(final ScanRepository scanRepo, final ShareScanRepository shareScanRepo) {
        this.scanRepo = scanRepo;
        this.shareScanRepo = shareScanRepo;
    }

    /**
     * Check if a user can access an application
     * 
     * @param user the user
     * @param appId the appId
     * @return boolean canAccess
     */
    public boolean canAccess(final Principal user, final long appId) {
        final Long scanAccountId = scanRepo.getScanAccountId(appId);
        final long accountId = UserPrincipal.getAccountId(user);

        if (scanAccountId == null) {
            logError(appId, user);
            return false;
        } else if (user.isInternal() || accountId == scanAccountId) {
            return true;
        }
        final List<ShareScan> shared = shareScanRepo.findByAppIdAndAccountId(appId, accountId);
        if (shared.isEmpty()) {
            logError(appId, user);
            return false;
        } else {
            return true;
        }
    }

    /**
     * check if a user can mitigate the scan
     * 
     * @param user the user
     * @param appId the appId
     * @param action the action
     * @return boolean canMitigate
     */
    public boolean canMitigate(final Principal user, final long appId, final MitigationAction action) {
        // First check if user is authorized for the mitigation action
        if ((action == MitigationAction.APPROVE || action == MitigationAction.REJECT)
                && !UserPrincipal.hasAuthority(user, "approveMitigations")) {
            logError(appId, user);
            return false;
        }
        // verify if the appId exist and the has completed scans
        final Scan scan = scanRepo.findFirstByAppIdAndStatus(appId, ScanStatus.COMPLETED);
        final long accountId = UserPrincipal.getAccountId(user);
        if (scan == null) {
            logError(appId, user);
            return false;
        } else if (user.isInternal() || accountId == scan.getAccountId()) {
            return true;
        }
        // check if the scan was shared for Cots/Vast scan if so allow enterprise account access application
        final int shared = shareScanRepo.countByAppIdAndAccountId(appId, accountId);
        if (shared > 0) {
            return true;
        }

        logError(appId, user);
        return false;
    }

    /**
     * Check if a user can access an application
     *
     * @param user the user
     * @param appId the appId
     * @param policy the policy
     * @return boolean canAccess
     */
    public boolean canAccess(final Principal user, final long appId, final ScanPolicy policy) {
        final long userAccountId = UserPrincipal.getAccountId(user);
        if (policy == null) {
            final Long scanAccountId = scanRepo.getScanAccountId(appId);
            if (scanAccountId == null) {
                logError(appId, user);
                return false;
            } else if (user.isInternal() || userAccountId == scanAccountId) {
                return true;
            }
        } else {
            if (user.isInternal() || userAccountId == policy.getAccountId()) {
                return true;
            }
        }
        final List<ShareScan> shared = shareScanRepo.findByAppIdAndAccountId(appId, userAccountId);
        if (shared.isEmpty()) {
            logError(appId, user);
            return false;
        } else {
            return true;
        }
    }

    /**
     * 
     * @param objectId the objectId
     * @param user the user
     * 
     */
    private static void logError(final long objectId, final Principal user) {
        LOG.warn("Invalid access to entity " + TYPE + " " + objectId + " from account " + UserPrincipal.getAccountId(user)
                + " login account " + user.getUserId());
    }

}
