package com.veracode.sca.scanresult.authz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.veracode.agora.identity.auth.Principal;
import com.veracode.sca.scanresult.domain.Component;
import com.veracode.sca.scanresult.repo.ComponentRepository;
import com.veracode.sca.scanresult.util.UserPrincipal;
import com.veracode.security.logging.SecureLogger;

import java.util.Optional;

/**
 * 
 * Authorization of a user to access component information
 *
 */
@Service("authorizeComponent")
public class AuthorizeComponent {

    private static final SecureLogger LOG = SecureLogger.getLogger(AuthorizeComponent.class);
    private static final String TYPE = "component";
    private final ComponentRepository componentRepo;

    /**
     * Authorize component
     * 
     * @param componentRepo the ComponentRepository
     */
    @Autowired
    public AuthorizeComponent(final ComponentRepository componentRepo) {
        this.componentRepo = componentRepo;
    }

    /**
     * Check if a user can access a component
     * 
     * @param user the user
     * @param componentId the componentId
     * @return boolean canAccess
     */
    public boolean canAccess(final Principal user, final String componentId) {
        final Optional<Component> optionalComponent = componentRepo.findById(componentId);
        if (optionalComponent.isPresent()) {
            return true;
        }
        logError(componentId, user);
        return false;
    }

    /**
     * 
     * @param objectId the objectId
     * @param user the user
     * 
     */
    private static void logError(final String objectId, final Principal user) {
        LOG.warn("Invalid access to " + TYPE + " " + objectId + " from account " + UserPrincipal.getAccountId(user)
                + " login account " + user.getUserId());
    }
}
