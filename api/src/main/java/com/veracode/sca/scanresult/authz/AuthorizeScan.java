package com.veracode.sca.scanresult.authz;

import java.util.List;

import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.veracode.agora.identity.auth.Principal;
import com.veracode.sca.scanresult.domain.MitigationAction;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanStatus;
import com.veracode.sca.scanresult.domain.ShareScan;
import com.veracode.sca.scanresult.repo.ScanRepository;
import com.veracode.sca.scanresult.repo.ShareScanRepository;
import com.veracode.sca.scanresult.util.UserPrincipal;
import com.veracode.security.logging.SecureLogger;

/**
 * 
 * Authorization of a user to access scan information
 *
 */
@SuppressWarnings({"PMD.CyclomaticComplexity"})
@Service("authorizeScan")
public class AuthorizeScan {

    private static final SecureLogger LOG = SecureLogger.getLogger(AuthorizeScan.class);
    private final ScanRepository scanRepo;
    private final ShareScanRepository shareScanRepo;
    private static final String TYPE = "scan";

    /**
     * Authorize scan
     * 
     * @param scanRepo the scanRepo
     * @param shareScanRepo the shareScanRepo
     */
    @Autowired
    public AuthorizeScan(final ScanRepository scanRepo, final ShareScanRepository shareScanRepo) {
        this.scanRepo = scanRepo;
        this.shareScanRepo = shareScanRepo;
    }

    /**
     * Check if a user can access a scan
     * 
     * @param user the user
     * @param scanId the scanId
     * @return boolean canAccess
     */
    public boolean canAccess(final Principal user, final String scanId) {
        final Scan scan = scanRepo.findByScanId(scanId);
        if (scan == null) {
            logError(scanId, user);
            return false;
        }

        if (scan.getDeleted()) {
            LOG.warn("Scan is deleted, scanId: " + scan.getScanId());
        }

        if (scan.getStatus().equals(ScanStatus.COMPLETED)) {
            if (user.isInternal()) {
                return true;
            }
            final long accountId = UserPrincipal.getAccountId(user);
            if (accountId == scan.getAccountId()
                    && (scan.getScaEnabled() || UserPrincipal.hasAuthority(user, "FEATURE_SCA_ENABLED"))) {
                return true;
            }

            final ShareScan shared = shareScanRepo.findFirstByScanIdAndAccountIdOrderByShareTsDesc(scanId, accountId);
            if (shared == null) {
                logError(scanId, user);
                return false;
            } else {
                return true;
            }

        }
        logError(scanId, user);
        return false;
    }

    public boolean canAccessScan(final Principal user, final String scanId){
        final Scan scan = scanRepo.findByScanId(scanId);
        if (scan == null) {
            logError(scanId, user);
            return false;
        }

        if (scan.getDeleted()) {
            LOG.warn("Scan is deleted, scanId: " + scan.getScanId());
        }

        return canAccessScan(user, scan);
    }

    public boolean canAccessScan(final Principal user, final Scan scan) {
        String scanId = scan.getScanId();

        if (user.isInternal()){
            return true;
        }

        final long accountId = UserPrincipal.getAccountId(user);
        if (accountId == scan.getAccountId()
                && (scan.getScaEnabled() || UserPrincipal.hasAuthority(user, "FEATURE_SCA_ENABLED"))) {
            return true;
        }

        final ShareScan shared = shareScanRepo.findFirstByScanIdAndAccountIdOrderByShareTsDesc(scanId, accountId);
        if (shared == null) {
            logError(scanId, user);
            return false;
        } else {
            return true;
        }

    }

    /**
     * Check if a user can create a scan
     * 
     * @param user the user
     * @return boolean canCreateScan
     */
    public boolean canCreateScan(final Principal user) {
        if (user == null || user.getAuthorities() == null) {
            return false;
        }
        return UserPrincipal.hasAuthority(user, "submitScans");
    }
    
    /**
     * check if a user can mitigate the scan
     * 
     * @param user the user
     * @param scan the scan
     * @param action the action
     * @return boolean canMitigate
     */
    public boolean canMitigate(final Principal user, final Scan scan, final MitigationAction action) {
        // verify this is the latest completed scan
        if (scan == null) {
            logError("scan", user);
            return false;
        }
        if (scan.getDeleted()) {
            LOG.warn("Scan is deleted, scanId: " + scan.getScanId());
        }

        final List<Scan> scans = scanRepo.findByAppIdAndStatusAndCompletedTsGreaterThan(scan.getAppId(),
            ScanStatus.COMPLETED, scan.getCompletedTs());
        if (!scans.isEmpty()) {
            LOG.warn("There is new scan completed after scanId: " + scan.getScanId());
        }

        if (scan != null && scan.getStatus().equals(ScanStatus.COMPLETED)) {
            final long accountId = UserPrincipal.getAccountId(user);
            if (user.isInternal() || accountId == scan.getAccountId()) {
                return true;
            }
            // allow enterprise to add comments
            if (action == MitigationAction.COMMENT) {
                final List<ShareScan> shared = shareScanRepo.findByScanIdAndAccountId(scan.getScanId(), accountId);
                if (shared.isEmpty()) {
                    logError(scan.getScanId(), user);
                    return false;
                } else {
                    return true;
                }
            }
        }
        logError(scan.getScanId(), user);
        return false;
    }

    /**
     * This method is to check whether user(actor) has permission to delete the scan. Method expect user and scan are
     * non null values.
     */
    public boolean canDeleteScan(@Nonnull final Principal user, @Nonnull final Scan scan) {
        if ((user.isInternal() || user.getOrganizationId() == scan.getAccountId())
                && ((scan.isSandbox() && UserPrincipal.hasAuthority(user, "deleteSandboxScan")
                        || UserPrincipal.hasAuthority(user, "deletePolicyScan"))
                        // TODO: remove this once the VOSP changes are made, added since vosp sends a constructed
                        // principal. (SCALK-2093)
                        || UserPrincipal.hasAuthority(user, "SCANRESULT"))) {
            return true;
        }
        logError(scan.getScanId(), user);
        return false;
    }

    /**
     * This method is to check whether user(actor) is internal and has authority to delete the scan.
     * Method expect user and scan are non null values.
     */
    public boolean canUndeleteScanInternalOnly(@Nonnull final Principal user, @Nonnull final Scan scan) {
        if (user.isInternal() && ((scan.isSandbox() && UserPrincipal.hasAuthority(user, "deleteSandboxScan")
                || UserPrincipal.hasAuthority(user, "deletePolicyScan"))
                // TODO: remove this once the VOSP changes are made, added since vosp sends a constructed
                // principal. (SCALK-2093)
                || UserPrincipal.hasAuthority(user, "SCANRESULT"))) {
            return true;
        }
        logError(scan.getScanId(), user);
        return false;
    }

    /**
     * Check if user can delete scans related to a app
     *
     * @param user the user
     * @return
     */
    public boolean canDelete(final Principal user, final Long appId) {
        if(user.isInternal()) {
            return true;
        }

        final Scan scan = scanRepo.findByAppId(appId).get(0);

        final long accountId = UserPrincipal.getAccountId(user);
        if (scan != null && accountId == scan.getAccountId()
                && (scan.getScaEnabled() || UserPrincipal.hasAuthority(user, "FEATURE_SCA_ENABLED"))) {
            if(scan.isSandbox()) {
                return UserPrincipal.hasAuthority(user, "DELETESANDBOXSCAN");
            } else {
                return UserPrincipal.hasAuthority(user, "DELETEPOLICYSCAN");
            }
        }

        LOG.warn("Permission denied for this action");
        return false;
    }

    private static void logError(final String objectId, final Principal user) {
        LOG.warn("Invalid access to " + TYPE + " " + objectId + " from account " + UserPrincipal.getAccountId(user)
                + " login account " + user.getUserId());
    }

}
