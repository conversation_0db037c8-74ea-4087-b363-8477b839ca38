package com.veracode.sca.scanresult.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.veracode.sca.scanresult.platform.PlatformAuthInterceptor;
import com.veracode.sca.scanresult.platform.client.PlatformClient;
import com.veracode.sca.scanresult.registry.client.RegistryClient;
import nl.altindag.ssl.SSLFactory;
import nl.altindag.ssl.util.Apache4SslUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.bouncycastle.jcajce.provider.BouncyCastleFipsProvider;
import org.bouncycastle.jsse.provider.BouncyCastleJsseProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.nio.file.Paths;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;
import static com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS;
import static com.veracode.sca.scanresult.config.RestTemplateHelper.configurePageResponseMixIn;
import static java.nio.file.Files.newInputStream;
import static java.util.Objects.nonNull;

@Configuration
public class ClientConfigs {

    private static final int DEFAULT_CONNECT_TIMEOUT = (int) TimeUnit.SECONDS.toMillis(10);
    private static final int DEFAULT_READ_TIMEOUT = (int) TimeUnit.SECONDS.toMillis(30);

    @Value("${srcclr.registry-api-url}")
    private String registryApiUrl;

    @Value("${platform.plt-api-url}")
    private String platformApiUrl;

    @Autowired
    private ApplicationContext context;


    /**
     * This would configure Platform client to http connection.
     */
    @Bean
    public PlatformClient platformClient() {
        final var restTemplate = restTemplate();
        restTemplate.getInterceptors().add(context.getBean(PlatformAuthInterceptor.class));
        return new PlatformClient(restTemplate, platformApiUrl);
    }
    
    /**
     * Load the bean only if mtls.enabled = false. This would configure registry client to http connection
     * without mtls.
     */
    @Bean
    @ConditionalOnProperty(value = {"mtls.enabled"}, havingValue = "false")
    public RegistryClient registryClient() {
        final var restTemplate = restTemplate();
        configurePageResponseMixIn(restTemplate,
                com.veracode.sca.scanresult.registry.objects.PageResponse.class);
        return new RegistryClient(restTemplate, registryApiUrl);
    }

    /**
     * Load the bean only if mtls.enabled = true. This would configure registry client to http connection
     * with mtls.
     */
    @Bean
    @ConditionalOnProperty(value = {"mtls.enabled"}, havingValue = "true")
    public RegistryClient registryClientMtls(@Value("${mtls.ssl.key-store}") String keyStorePath,
                                             @Value("${mtls.ssl.key-store-password}") char[] keyStorePassword,
                                             @Value("${mtls.ssl.trust-store}") String trustStorePath,
                                             @Value("${mtls.ssl.trust-store-password}") char[] trustStorePassword) throws Exception {

        final var restTemplate = restTemplateMtls(keyStorePath, keyStorePassword, trustStorePath, trustStorePassword);
        configurePageResponseMixIn(restTemplate,
                com.veracode.sca.scanresult.registry.objects.PageResponse.class);
        return new RegistryClient(restTemplate, registryApiUrl);
    }

    /**
     * @param requestFactoryConfigurer if non-null, can be used to set additional configuration on the
     *                                 {@code ClientHttpRequestFactory} used by the RestTemplate. Such options might
     *                                 include user-defined timeout values in lieu of the defaults defined above, or
     *                                 updates to the buffering strategy.
     * @return a {@code RestTemplate} configured with a {@link SimpleClientHttpRequestFactory} and reasonable defaults
     * set for timeouts, optionally overridden by additional configuration passed via the
     * {@code requestFactoryConfigurer} parameter
     * @see RestTemplate#setRequestFactory(ClientHttpRequestFactory)
     */
    public static RestTemplate restTemplate(@Nullable Consumer<SimpleClientHttpRequestFactory> requestFactoryConfigurer) {
        final SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
        requestFactory.setReadTimeout(DEFAULT_READ_TIMEOUT);
        if (requestFactoryConfigurer != null) {
            requestFactoryConfigurer.accept(requestFactory);
        }

        return new RestTemplate(requestFactory);
    }


    public RestTemplate restTemplate() {
        return restTemplate(requestFactory ->
                requestFactory.setBufferRequestBody(false));
    }


    public RestTemplate restTemplateMtls(String keyStorePath, char[] keyStorePassword, String trustStorePath,
                                         char[] trustStorePassword) throws Exception {
        final var sslFactory = sslFactory(keyStorePath, keyStorePassword, trustStorePath, trustStorePassword);
        final var httpClient = apacheHttpClient(sslFactory);
        final var clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClient);
        clientHttpRequestFactory.setBufferRequestBody(false);
        clientHttpRequestFactory.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
        clientHttpRequestFactory.setReadTimeout(DEFAULT_READ_TIMEOUT);
        clientHttpRequestFactory.setBufferRequestBody(false);
        return new RestTemplate(clientHttpRequestFactory);
    }

    /**
     * Build the ssl factory for SSL connection .
     */
    private SSLFactory sslFactory(String keyStorePath, char[] keyStorePassword, String trustStorePath, char[] trustStorePassword) throws Exception {
        return SSLFactory.builder()
                .withDefaultTrustMaterial()
                .withSecurityProvider(new BouncyCastleJsseProvider(true, new BouncyCastleFipsProvider()))
                .withTrustMaterial(newInputStream(Paths.get(trustStorePath)), trustStorePassword)
                .withIdentityMaterial(newInputStream(Paths.get(keyStorePath)), keyStorePassword)
                .withProtocols("TLSv1.2")
                .build();
    }

    /**
     * Adding a default ObjectMapper so it doesn't conflict with Agora service base
     */
    @Bean
    public ObjectMapper objectMapper() {
        final ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
        objectMapper.configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new Jdk8Module());
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper;
    }

    /**
     * Builds the apache http client
     */
    private CloseableHttpClient apacheHttpClient(final SSLFactory sslFactory) {
        if (nonNull(sslFactory)) {
            final var socketFactory = Apache4SslUtils.toSocketFactory(sslFactory);
            return HttpClients.custom()
                    .setSSLSocketFactory(socketFactory)
                    .build();
        } else {
            return HttpClients.createDefault();
        }
    }
}