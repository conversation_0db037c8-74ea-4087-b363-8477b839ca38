package com.veracode.sca.scanresult.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Configuration for read-only data source
 */
@Configuration
public class ReadOnlyDataSourceConfiguration {

    @Bean
    @ConfigurationProperties("spring.datasource.read-only-db")
    public DataSourceProperties readOnlyDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean
    @Qualifier("readOnlyDataSource")
    @ConfigurationProperties("spring.datasource.read-only-db.hikari")
    public DataSource readOnlyDataSource() {
        return readOnlyDataSourceProperties()
                .initializeDataSourceBuilder()
                .build();
    }
}
