package com.veracode.sca.scanresult.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Configuration for read-write data source
 */
@Configuration
public class ReadWriteDataSourceConfiguration {

    @Bean
    @ConfigurationProperties("spring.datasource.read-write-db")
    public DataSourceProperties readWriteDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean
    @Qualifier("readWriteDataSource")
    @ConfigurationProperties("spring.datasource.read-write-db.hikari")
    public DataSource readWriteDataSource() {
        return readWriteDataSourceProperties()
                .initializeDataSourceBuilder()
                .build();
    }
}

