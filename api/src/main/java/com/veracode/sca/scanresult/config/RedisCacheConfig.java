package com.veracode.sca.scanresult.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.veracode.security.logging.SecureLogger;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.interceptor.SimpleCacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;

@Configuration
@Import({ RedisAutoConfiguration.class })
@EnableConfigurationProperties(RedisCacheConfigurationProperties.class)
@ConditionalOnProperty(prefix = "spring", name = "cache.type", havingValue = "redis")
public class RedisCacheConfig extends CachingConfigurerSupport {

    private static final SecureLogger LOGGER = SecureLogger.getLogger(RedisCacheConfig.class);

    private static final String CACHE_PREFIX = "scanresult-service-caches";

    private static RedisCacheConfiguration createCacheConfiguration(
        String cacheName, RedisCacheConfigurationProperties.CacheConfiguration cacheConfiguration) {

        var defaultRedisConfiguration = RedisCacheConfiguration.defaultCacheConfig()
                .disableCachingNullValues()
                .entryTtl(Duration.ofSeconds(cacheConfiguration.getExpiration()))
                .prefixCacheNameWith(CACHE_PREFIX + ":");

        if (cacheConfiguration.getType() != null) {
            try {
                // We use a special serializer to serialize classes that are difficult to serialize
                var objectMapper = new ObjectMapper().registerModules(new Jdk8Module(), new JavaTimeModule());
                var jacksonSerializer = new Jackson2JsonRedisSerializer<>(Class.forName(cacheConfiguration.getType()));
                jacksonSerializer.setObjectMapper(objectMapper);
                return defaultRedisConfiguration
                        .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jacksonSerializer));
            } catch (ClassNotFoundException e) {
                LOGGER.error("Failed to get class type for cache category! Cache: {} | Type: {}",
                        cacheName, cacheConfiguration.getType(), e);
            }
        }

        return defaultRedisConfiguration;
    }

    @Bean
    public CacheManager cacheManager(
            RedisConnectionFactory redisConnectionFactory, RedisCacheConfigurationProperties redisCacheConfigurationProperties) {

        var redisCacheManagerBuilder = RedisCacheManager.builder(redisConnectionFactory);

        var cacheables = redisCacheConfigurationProperties.getCacheables();
        if (cacheables == null || cacheables.isEmpty()) {
            return redisCacheManagerBuilder.build();
        }

        Map<String, RedisCacheConfiguration> cacheablesConfigurations = new HashMap<>();
        for (Map.Entry<String, RedisCacheConfigurationProperties.CacheConfiguration> cacheable : cacheables.entrySet()) {
            cacheablesConfigurations.put(cacheable.getKey(), createCacheConfiguration(cacheable.getKey(), cacheable.getValue()));
        }

        return redisCacheManagerBuilder.withInitialCacheConfigurations(cacheablesConfigurations).build();
    }

    /**
     * The default behavior is for Redis to throw a RuntimeException on
     *  serialization or deserialization errors, preventing the execution from completing.
     *  This default behavior is overridden below to allow the execution to fetch data
     *  from the backend services (or database) when the caching layer throws an error.
     */
    @Override
    public org.springframework.cache.interceptor.CacheErrorHandler errorHandler() {
        return new RedisErrorHandler();
    }

    private static class RedisErrorHandler extends SimpleCacheErrorHandler {
        @Override
        public void handleCacheGetError(@NotNull RuntimeException exception, Cache cache, @NotNull Object key) {
            LOGGER.warn("Deserialization error while getting '{}' from '{}' cache. Fetching data from the backend!",
                    key, cache.getName(), exception);
        }

        @Override
        public void handleCachePutError(@NotNull RuntimeException exception, Cache cache, @NotNull Object key, Object value) {
            LOGGER.warn("Serialization error while putting '{}' in '{}' cache. Fetching data from the backend!",
                    key, cache.getName(), exception);
        }

        @Override
        public void handleCacheEvictError(@NotNull RuntimeException exception, @NotNull Cache cache, @NotNull Object key) {
            LOGGER.warn("Experienced an error while evicting '{}' from '{}' cache!", key, cache.getName(), exception);
            // Keeping the default behavior for now
            throw exception;
        }

        @Override
        public void handleCacheClearError(@NotNull RuntimeException exception, @NotNull Cache cache) {
            LOGGER.warn("Experienced an error while clearing '{}' cache!", cache.getName(), exception);
            // Keeping the default behavior for now
            throw exception;
        }
    }
}
