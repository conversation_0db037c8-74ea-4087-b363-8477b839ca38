package com.veracode.sca.scanresult.config;

import com.veracode.security.logging.SecureLogger;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;
import java.util.Optional;

@ConfigurationProperties(prefix = "redis")
public class RedisCacheConfigurationProperties {

    private static final SecureLogger LOGGER = SecureLogger.getLogger(RedisCacheConfigurationProperties.class);

    private static Map<String, CacheConfiguration> cacheables;

    public Map<String, CacheConfiguration> getCacheables() {
        return cacheables;
    }

    public void setCacheables(Map<String, CacheConfiguration> cacheables) {
        RedisCacheConfigurationProperties.cacheables = cacheables;
    }

    public static Optional<CacheConfiguration> getCacheable(String cache) {
        try {
            return Optional.of(cacheables.get(cache));
        } catch (NullPointerException nullPointerException) {
            LOGGER.warn("Caching Configurations are missing for '{}'! Caching will be disabled!", cache);
            return Optional.empty();
        }
    }

    public static class CacheConfiguration {

        private boolean enabled;

        private long expiration;

        private String type;

        /**
         * Caching is disabled by default
         * @return a boolean describing whether the cache category is enabled
         */
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        /**
         * The TTL for the cache is seconds
         * @return a long of seconds
         */
        public long getExpiration() {
            return expiration;
        }

        public void setExpiration(long expiration) {
            this.expiration = expiration;
        }

        /**
         * Specify to use Jackson2JsonRedisSerializer, but only if default serialization is not working. Simply using
         * `type: java.lang.Object` for some classes might work, as long as the return type is not a Page, List, etc.
         * Otherwise, the exact type of the class can be specified. For example, `com.sourceclear.api.data.evidence.LibraryModel`
         * @return The class type of the object to be serialized as a string
         */
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}