package com.veracode.sca.scanresult.config;

import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;

public class RestTemplateHelper {

  /**
   * We need to elide certain fields that are passed back to the caller from the remote service response after
   * those services have their version of Spring Boot upgraded. These include new versions of spring-data which
   * causes deserialization errors for classes that extend {@link PageImpl}. Examples
   * are {@link com.sourceclear.registry.objects.PageResponse} implementations in several micro services.
   *
   * This Jackson "mix-in" suppresses deserialization of these fields (by tagging them as JsonIgnore), which are,
   * for now at least, not needed when handling the responses.
   */
  static abstract class ForwardCompatiblePageResponseMixIn {
    @JsonProperty
    @JsonIgnore
    Object pageable;

    @JsonProperty
    @JsonIgnore
    Object sort;

    @JsonProperty
    @JsonIgnore
    public abstract void setSort(Sort sort);
  }

  public static <T extends PageImpl<?>> void configurePageResponseMixIn(RestTemplate restTemplate, Class<T> klass) {
    restTemplate.getMessageConverters().stream()
        .filter(c -> c instanceof MappingJackson2HttpMessageConverter)
        .map(MappingJackson2HttpMessageConverter.class::cast)
        .findFirst()
        .ifPresent(messageConverter -> {
          final ObjectMapper objectMapper = messageConverter.getObjectMapper();
          objectMapper.addMixIn(klass, ForwardCompatiblePageResponseMixIn.class);
        });
  }

}
