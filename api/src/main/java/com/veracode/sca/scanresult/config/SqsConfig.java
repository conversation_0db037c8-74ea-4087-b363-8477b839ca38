/* © 2024 Veracode, Inc. */
package com.veracode.sca.scanresult.config;

import com.amazon.sqs.javamessaging.ProviderConfiguration;
import com.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSAsync;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.sourceclear.util.messaging.MessagingService;
import com.sourceclear.util.messaging.MessagingServiceImpl;
import com.sourceclear.util.messaging.QueueManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.support.destination.DynamicDestinationResolver;

import javax.jms.Session;

@Configuration
@EnableJms
public class SqsConfig {

  private AWSCredentialsProvider awsCredentialsProvider;

  @Value("${cloud.aws.region.static}")
  private String region;

  @Autowired
  public SqsConfig(AWSCredentialsProvider awsCredentialsProvider) {
    this.awsCredentialsProvider = awsCredentialsProvider;
  }

  @Bean("QueueManager")
  @Autowired
  public QueueManager queueManager(MessagingService messagingService) {
    return new QueueManager(messagingService);
  }

  @Bean("MessagingService")
  @Autowired
  public MessagingService messagingService(
      AmazonSQSAsync amazonSqsAsync, @Qualifier("AmazonSQS") AmazonSQS amazonSQS) {
    return new MessagingServiceImpl(amazonSqsAsync, amazonSQS, true);
  }

  @Bean("AmazonSQS")
  public AmazonSQS amazonSqs() {
    return AmazonSQSClientBuilder.standard()
        .withCredentials(awsCredentialsProvider)
        .withRegion(region)
        .build();
  }


  @Bean("JmsListenerContainerFactory-S3")
  @DependsOn("AmazonSQS")
  public DefaultJmsListenerContainerFactory jmsListenerContainerFactoryAndS3(
      @Qualifier("AmazonSQS") AmazonSQS amazonSQS) {
    DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
    factory.setConnectionFactory(new SQSConnectionFactory(new ProviderConfiguration(), amazonSQS));
    factory.setDestinationResolver(new DynamicDestinationResolver());
    factory.setConcurrency("3-10");
    factory.setSessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE);
    return factory;
  }
}
