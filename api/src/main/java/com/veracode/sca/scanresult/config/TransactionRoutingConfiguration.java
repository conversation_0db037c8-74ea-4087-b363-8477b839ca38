package com.veracode.sca.scanresult.config;

import com.veracode.sca.scanresult.config.transaction.routing.CustomTransactionManager;
import com.veracode.sca.scanresult.config.transaction.routing.DataSourceType;
import com.veracode.sca.scanresult.config.transaction.routing.TransactionRoutingDataSource;
import net.ttddyy.dsproxy.support.ProxyDataSourceBuilder;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaDialect;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.TransactionTemplate;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
@EnableTransactionManagement
public class TransactionRoutingConfiguration {

    @Bean
    @Primary
    @Qualifier("customTransactionRoutingDataSource")
    public TransactionRoutingDataSource actualDataSource(@Qualifier("readWriteDataSource") DataSource readWriteDataSource,
                                                         @Qualifier("readOnlyDataSource") DataSource readOnlyDataSource) {
        TransactionRoutingDataSource transactionRoutingDataSource = new TransactionRoutingDataSource();
        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put(DataSourceType.READ_WRITE, readWriteDataSource);
        dataSourceMap.put(DataSourceType.READ_ONLY, readOnlyDataSource);

        transactionRoutingDataSource.setTargetDataSources(dataSourceMap);
        return transactionRoutingDataSource;
    }

    private DataSource dataSource(DataSource transactionRoutingDataSource) {
        return ProxyDataSourceBuilder
                .create(transactionRoutingDataSource)
                .build();
    }

    private String[] packagesToScan() {
        return new String[]{
                "com.veracode.sca.scanresult"
        };
    }

    private Properties additionalProperties() {
        Properties properties = new Properties();
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.PostgreSQL9Dialect");

        //For Hibernate 5, allow table field to be read following underscore format instead of Java camel case.
        properties.setProperty("hibernate.physical_naming_strategy", "com.veracode.sca.scanresult.config.hibernate.PhysicalNamingStrategyImpl");

        properties.setProperty("hibernate.jdbc.use_get_generated_keys", "true");
        properties.setProperty("hibernate.temp.use_jdbc_metadata_defaults", "false");

        return properties;
    }

    @Bean(name="entityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryBean(@Qualifier("customTransactionRoutingDataSource") TransactionRoutingDataSource transactionRoutingDataSource) {
        LocalContainerEntityManagerFactoryBean localContainerEntityManagerFactoryBean =
                new LocalContainerEntityManagerFactoryBean();
        localContainerEntityManagerFactoryBean.setPersistenceUnitName("custom-2DBsources");
        localContainerEntityManagerFactoryBean.setPersistenceProvider(new HibernatePersistenceProvider());
        localContainerEntityManagerFactoryBean.setDataSource(dataSource(transactionRoutingDataSource));
        localContainerEntityManagerFactoryBean.setPackagesToScan(packagesToScan());

        HibernateJpaVendorAdapter hibernateJpaVendorAdapter = new HibernateJpaVendorAdapter();
        HibernateJpaDialect hibernateJpaDialect = hibernateJpaVendorAdapter.getJpaDialect();
        hibernateJpaDialect.setPrepareConnection(false);
        localContainerEntityManagerFactoryBean.setJpaVendorAdapter(hibernateJpaVendorAdapter);
        localContainerEntityManagerFactoryBean.setJpaProperties(additionalProperties());
        localContainerEntityManagerFactoryBean.afterPropertiesSet();

        return localContainerEntityManagerFactoryBean;
    }

    @Bean
    public CustomTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {
        CustomTransactionManager transactionManager = new CustomTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory);
        return transactionManager;
    }

    @Bean
    public TransactionTemplate transactionTemplate(EntityManagerFactory entityManagerFactory) {
        return new TransactionTemplate(transactionManager(entityManagerFactory));
    }
}
