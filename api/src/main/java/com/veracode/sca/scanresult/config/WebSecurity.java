package com.veracode.sca.scanresult.config;

import static com.veracode.agora.servicebase.util.ConstantUtil.TOMCAT_SERVER_PORT;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;

import com.veracode.agora.servicebase.auth.security.config.BaseWebSecurity;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurity extends BaseWebSecurity {

    @Value("${" + TOMCAT_SERVER_PORT + "?:8509}")
    private int httpsPort;

    @Value("${server.ssl.enabled:true}")
    private boolean sslEnabled;

    @Override
    protected void configure(final HttpSecurity http) throws Exception {

        if (sslEnabled) {
            http.requiresChannel().anyRequest().requiresSecure();
        }

        http
            // disable crsf checking, since the service runs stateless and doesn't do any validation/auth based on
            // session/csrf token, auth happens based on the authentication token.
            .csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
            // Configure our Authentication provider
            .addFilter(requestHeaderAuthenticationFilter(authenticationManager()))
            .authenticationProvider(preAuthenticatedAuthenticationProvider()).authorizeRequests()
                .antMatchers("/**/api-docs").permitAll()
                .antMatchers("/**/actuator/**").permitAll()
                .antMatchers("/**/swagger-ui.html").permitAll()
                .antMatchers("/**/webjars/**").permitAll()
                .antMatchers("/**/swagger-resources/**").permitAll()
                .antMatchers("/**").authenticated();
    }

    /**
     * @see BaseWebSecurity#configure(org.springframework.security.config.annotation.web.builders.WebSecurity)
     */
    @Override
    public void configure(final org.springframework.security.config.annotation.web.builders.WebSecurity web) {
        /*
         * Configure spring security to completely ignore /health endpoint - this way it is considered sensitive for
         * both authenticated and unauthenticated users
         */
        web.ignoring().antMatchers("/**/actuator/**");
    }

    @Override
    public void configureGlobal(final AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(this.preAuthenticatedAuthenticationProvider());
    }
}
