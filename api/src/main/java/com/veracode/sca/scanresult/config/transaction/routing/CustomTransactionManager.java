package com.veracode.sca.scanresult.config.transaction.routing;

import com.veracode.security.logging.SecureLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.DelegatingTransactionAttribute;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.persistence.EntityManagerFactory;

public class CustomTransactionManager extends JpaTransactionManager {

    private static final SecureLogger LOG = SecureLogger.getLogger(CustomTransactionManager.class);

    public CustomTransactionManager() {
        super();
    }

    public CustomTransactionManager(EntityManagerFactory emf) {
        super(emf);
    }

    /**
     * When transaction is started, need to set the current transaction to read-only if it is read-only and is
     * labeled to be run as read only. See {@link TransactionLabel}.
     * Let TransactionSynchronizationManager know, then proceed to doBegin.
     *
     * @param transaction the transaction object returned by {@code doGetTransaction}
     * @param definition a TransactionDefinition instance, describing propagation
     * behavior, isolation level, read-only flag, timeout, and transaction name
     */
    @Override
    protected void doBegin(Object transaction, TransactionDefinition definition) {
        if (StringUtils.isNotEmpty(definition.getName()) &&
                ((DelegatingTransactionAttribute) definition).getLabels().stream().anyMatch(s -> s.contentEquals(TransactionLabel.RUN_AS_READONLY.name()))) {
            LOG.debug("Transaction Definition - Name is {} : Settings are {}", definition.getName(),
                    definition);
            TransactionSynchronizationManager.setCurrentTransactionIsolationLevel(
                    definition.getIsolationLevel() != TransactionDefinition.ISOLATION_DEFAULT
                            ? definition.getIsolationLevel()
                            : null);
            TransactionSynchronizationManager.setCurrentTransactionReadOnly(definition.isReadOnly());
            TransactionSynchronizationManager.setCurrentTransactionName(definition.getName());
        }
        super.doBegin(transaction, definition);
    }
}
