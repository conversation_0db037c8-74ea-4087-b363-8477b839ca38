package com.veracode.sca.scanresult.config.transaction.routing;

import com.veracode.security.logging.SecureLogger;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.transaction.support.TransactionSynchronizationManager;

public class TransactionRoutingDataSource extends AbstractRoutingDataSource {

    private static final SecureLogger LOG = SecureLogger.getLogger(TransactionRoutingDataSource.class);

    @Override
    protected Object determineCurrentLookupKey() {
        LOG.debug("Current Transaction ReadOnly: {}, Name: {}",
                TransactionSynchronizationManager.isCurrentTransactionReadOnly(),
                TransactionSynchronizationManager.getCurrentTransactionName());
        return TransactionSynchronizationManager.isCurrentTransactionReadOnly() ? DataSourceType.READ_ONLY :
                DataSourceType.READ_WRITE;
    }
}
