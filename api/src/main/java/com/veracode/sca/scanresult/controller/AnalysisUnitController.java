package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import com.fasterxml.jackson.annotation.JsonView;
import com.veracode.sca.scanresult.domain.JsonViews;
import com.veracode.sca.scanresult.domain.LinkedScanResult;
import com.veracode.sca.scanresult.domain.Mitigation;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanPromoteRequest;
import com.veracode.sca.scanresult.domain.ScanUpdate;
import com.veracode.sca.scanresult.domain.ShareScan;
import com.veracode.sca.scanresult.domain.ShareScanKey;
import com.veracode.sca.scanresult.exception.ScanResultProcessException;
import com.veracode.sca.scanresult.policy.PolicyComplianceModel;
import com.veracode.sca.scanresult.policy.domain.PolicyCompliance;
import com.veracode.sca.scanresult.policy.domain.PolicyComplianceData;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.report.domain.Filter;
import com.veracode.sca.scanresult.report.domain.IssueSeverity;
import com.veracode.sca.scanresult.report.domain.LinkedCommonScanVulnerability;
import com.veracode.sca.scanresult.report.domain.LinkedScanVulnerability;
import com.veracode.sca.scanresult.report.model.ScanComponentModel;
import com.veracode.sca.scanresult.report.model.ScanLicenseModel;
import com.veracode.sca.scanresult.report.service.ScanReportService;
import com.veracode.sca.scanresult.service.ScanResultService;
import com.veracode.sca.scanresult.service.ScanService;
import com.veracode.security.logging.SecureLogger;
import com.veracode.security.validation.VVGuid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.Positive;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api(value = "SCA scan API")
@RestController
@RequestMapping(value = {"/v1/analysis_units"}, produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class AnalysisUnitController {

    private static final SecureLogger LOG = SecureLogger.getLogger(AnalysisUnitController.class);

    private static final String ANALYSIS_UNIT_ID = "The scan identifier";
    private static final String PREV_ANALYSIS_UNIT_ID = "The previous scan identifier";
    private static final String PREV_SCAN_ID = "Previous Scan Identifier";
    private static final String SCAN_POLICY = "The scan policy";
    private static final String FILENAME_INFO = "Component file name filter";
    private static final String CVE_INFO = "Common vulnerabilities and exposures (CVE) identifier filter";
    private static final String COMPONENT_ID_INFO = "Unique identifier of the library";
    private static final String CVSS_VERSION_TYPE_INFO =
        "CVSS Score version type, if not passed in CVSS2 will be returned.";
    private static final String CVSS_VERSION_TYPE = "cvssType";

    private static final String SEVERITIES_INFO = "Severities filter";
    private static final String RISK_RATINGS_INFO = "Risk ratings filter";
    private static final String SUCCESS = "Success response";

    private static final String AU_ID = "analysis_unit_id";
    private static final String PREV_AU_ID = "prev_analysis_unit_id";
    private static final String PREV_SCAN = "prev_scan";
    private static final String FILENAME = "file_name";
    private static final String CVE_ID = "cve_id";
    private static final String COMPONENT_ID = "component_id";
    private static final String SEVERITIES = "severities";
    private static final String HAS_RISK_RATING_INFO = "Has Risk Rating";
    private static final String HAS_RISK_RATING = "has_risk_rating";
    private static final String RISK_RATINGS = "risk_ratings";
    private static final String POLICY = "policy";
    private static final String APP = "app_id";
    private static final String APP_INFO = "The application identifier";
    private static final String ACCOUNT = "account_id";
    private static final String ACCOUNT_INFO = "The account identifier";
    private static final String LICENSE_NAME = "license_name";
    private static final String LICENSE_NAME_INFO = "License name filter";
    private static final String HAS_LICENSE = "has_license";
    private static final String HAS_LICENSE_INFO = "Has license filter";
    private static final String GENERATED_TS = "generated_ts";
    private static final String GENERATED_TS_INFO = "Share report generated timestamp";

    private final ScanService scanService;
    private final PolicyComplianceService policyService;
    private final ScanReportService scanReportService;
    private final ScanResultService scanResultService;

    @Autowired
    public AnalysisUnitController(final ScanService scanService, final ScanReportService scanReportService,
            final PolicyComplianceService policyService, final ScanResultService scanResultService) {
        this.scanService = scanService;
        this.scanReportService = scanReportService;
        this.policyService = policyService;
        this.scanResultService = scanResultService;
    }

    @ApiOperation(value = "Get SCA Scan", notes = "Returns scan information", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Success response", response = Scan.class)})
    @GetMapping(value = "/{analysis_unit_id}/scan")
    public ResponseEntity<Scan> getScanFromAuId(@ApiParam(value = "Scan identifier",
        required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId) {
        final Scan scan = scanService.getScanByAnalysisUnitId(analysisUnitId);
        return new ResponseEntity<>(scan, HttpStatus.OK);
    }

    /**
     * Providing endpoint to mark the SCA scan as deleted (soft delete), since there can be multiple scan entries for
     * the same scan id, we will be deleting the latest scan out of them. Theoretically, there can be only one record of
     * the same analysis unit that's not deleted. And when undeleting, we should mark only the latest scan for the
     * analysis unit id.
     */
    @ApiOperation(value = "Delete SCA Scan", notes = "Delete SCA Scan for a given analysis unit id.",
        response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 204, message = "Successfully deleted")})
    @DeleteMapping(value = "/{analysis_unit_id}/scan")
    public ResponseEntity<Void> deleteScanByAuId(@ApiParam(value = "Scan identifier",
        required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId) {
        try {
            // Since VOSP is the only consumer and there are subsequent delete requests to delete the same scan, service
            // will return NO_CONTENT for all the requests.
            final Scan scan = scanService.getScanByAnalysisUnitIdIncludingDeleted(analysisUnitId);
            if (scan != null) {
                if (!scan.isDeleted()) {
                    scanService.deleteScan(scan);
                }
                return ResponseEntity.noContent().build();
            }
        } catch (ResourceNotFoundException e) {
            // ignoring if the resource not found.
        }
        return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
    }

    /**
     * Providing endpoint to patch the data elements of the SCA scan. As of now, this is used recover the deleted SCA
     * scan (soft deleted), since there can be multiple scan entries for the same scan id,only the latest scan for the
     * provided analysis unit id is marked as deleted false.
     */
    @ApiOperation(value = "Undelete SCA Scan", notes = "Undelete SCA Scan for a given analysis unit id.",
        response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Returns undeleted scan.", response = Scan.class)})
    @PatchMapping(value = "/{analysis_unit_id}/scan")
    public ResponseEntity<Scan> updateScanByAuId(
            @ApiParam(value = "AU Id", required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @RequestBody final ScanUpdate scanUpdate) {

        final Scan scan = scanService.getScanByAnalysisUnitIdIncludingDeleted(analysisUnitId);
        return ResponseEntity.ok(scanService.undeleteScan(scan, scanUpdate));
    }

    @ApiOperation(value = "Get policy compliance for the passing scan",
        notes = "Returns PolicyCompliance information and links to associated resources ",
        response = PolicyCompliance.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = PolicyCompliance.class)})
    @GetMapping(value = "/{analysis_unit_id}/scan:policycompliance")
    public PolicyCompliance getPolicycompliance(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final Long analysisUnitId,
            @ApiParam(value = APP_INFO) @RequestParam(value = APP, required = false) final Long appId,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion,
            @ApiParam(value = SCAN_POLICY, required = true) @RequestParam(value = POLICY) final ScanPolicy policy) {

        final String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        final ScanPolicy scanPolicy = getOrUseCurrentPolicy(policy);
        LOG.info("Policy compliance request analysisUnitId - " + analysisUnitId + " - scanId - " + scanId);
        return getPolicyCompliance(scanId,appId, scanPolicy, cvssVersion, true);
    }

    @ApiOperation(value = "Get policy compliance for the passing scan with time frame rule",
            notes = "Returns PolicyCompliance information and links to associated resources ",
            response = PolicyCompliance.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = PolicyCompliance.class)})
    @GetMapping(value = "/{analysis_unit_id}/scan:policycompliance/rule/timeframe")
    public ResponseEntity<PolicyComplianceData> getPolicyComplianceWithTimeFrameRule(
            @ApiParam(value = ANALYSIS_UNIT_ID, required = true) @Positive @PathVariable(
                    AU_ID) final Long analysisUnitId,
            @ApiParam(value = APP_INFO) @RequestParam(value = APP, required = false) final Long appId,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                    required = false) final CvssVersion cvssVersion,
            @ApiParam(value = SCAN_POLICY, required = true) @RequestParam(value = POLICY) final ScanPolicy policy) {

        if (policy.getEvaluationDate() == null) {
            // If time frame rule parameters are not sent with request, throws bad request error
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        final String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        final ScanPolicy scanPolicy = getOrUseCurrentPolicy(policy);

        LOG.info("Policy compliance request analysisUnitId - " + analysisUnitId + " - scanId - " + scanId + " - " + "evaluationDate - " + policy.getEvaluationDate());

        // policy evaluation With time frame rule applied
        final PolicyCompliance policyCompliance = getPolicyCompliance(scanId,appId, scanPolicy, cvssVersion, true);

        // policy evaluation without time frame rule
        scanPolicy.setEvaluationDate(null);
        scanPolicy.setEvaluationDateType(null);
        final PolicyCompliance policyComplianceWithOutTimeFrame = getPolicyCompliance(scanId,appId, scanPolicy, cvssVersion, false);

        return new ResponseEntity<>(new PolicyComplianceData(policyCompliance, policyComplianceWithOutTimeFrame), HttpStatus.OK);
    }
    
    @ApiOperation(value = "Get scan licenses for the passing scan",
        notes = "Returns ScanLicenses information and links to associated resources.")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{analysis_unit_id}/scan:licenses")
    public CollectionModel<ScanLicenseModel> getLicenses(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVE_ID, required = false) final String cveId,
            @ApiParam(value = LICENSE_NAME_INFO) @RequestParam(value = LICENSE_NAME,
                required = false) final String licenseName,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES,
                required = false) final IssueSeverity[] severities,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS,
                required = false) final String[] licenseRiskRatings,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final Filter criteria = new Filter.FilterBuilder().fileName(fileName).cveId(cveId).licenseName(licenseName)
            .severities(severities).licenseRiskRatings(licenseRiskRatings).cvssVersion(cvssVersion).createFilter();

        final Scan scan = scanService.getScanByAnalysisUnitId(analysisUnitId);
        final List<ScanLicenseModel> licenses =
            scanReportService.getLinkedScanLicenses(scan.getScanId(), getOrUseCurrentPolicy(policy), criteria);

        return CollectionModel.of(licenses);
    }

    @ApiOperation(value = "Get scan components for the passing scan",
        notes = "Returns ScanComponent information and links to associated resources ")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{analysis_unit_id}/scan:result")
    @JsonView(JsonViews.ComponentDetail.class)
    public ResponseEntity<LinkedScanResult> getResults(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = PREV_ANALYSIS_UNIT_ID) @Positive @RequestParam(value = PREV_AU_ID,
                required = false) final Long prevAnalysisUnitId,
            @ApiParam(value = PREV_SCAN_ID) @VVGuid @RequestParam(value = PREV_SCAN, required = false) final String prevScanIdProvided,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) CvssVersion cvssVersion) {

        final String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        final String prevScanId =
            prevAnalysisUnitId != null ? scanService.getScanByAnalysisUnitId(prevAnalysisUnitId).getScanId() : prevScanIdProvided;

        final LinkedScanResult result =
            scanReportService.getLinkedScanResults(scanId, prevScanId, getOrUseCurrentPolicy(policy), cvssVersion);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation(value = "Get scan components for the passing scan",
        notes = "Returns ScanComponent information and links to associated resources ")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{analysis_unit_id}/scan:components")
    @JsonView({JsonViews.ComponentSummary.class})
    public CollectionModel<EntityModel<ScanComponentModel>> getComponents(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVE_ID, required = false) final String cveId,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES,
                required = false) final IssueSeverity[] severities,
            @ApiParam(value = LICENSE_NAME_INFO) @RequestParam(value = LICENSE_NAME,
                required = false) final String licenseName,
            @ApiParam(value = HAS_LICENSE_INFO) @RequestParam(value = HAS_LICENSE,
                required = false) final Boolean hasLicense,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = HAS_RISK_RATING_INFO) @RequestParam(value = HAS_RISK_RATING,
                required = false) final Boolean hasLicenseRiskRating,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS,
                required = false) final String[] riskRatings,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final Filter criteria = new Filter.FilterBuilder().cveId(cveId).fileName(fileName).severities(severities)
            .licenseName(licenseName).hasLicense(hasLicense).hasLicenseRiskRating(hasLicenseRiskRating)
            .licenseRiskRatings(riskRatings).cvssVersion(cvssVersion).createFilter();
        String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        final List<ScanComponentModel> components =
            scanReportService.getLinkedScanComponents(scanId, getOrUseCurrentPolicy(policy), criteria);

        final List<Link> selfRefLinks = Collections.singletonList(
            linkTo(methodOn(AnalysisUnitController.class).getComponents(analysisUnitId, fileName, cveId, severities,
                licenseName, hasLicense, null, hasLicenseRiskRating, riskRatings, cvssVersion)).withSelfRel());

        List<EntityModel<ScanComponentModel>> resources =
            components.stream()
                .map(
                    component -> EntityModel.of(component,
                        linkTo(methodOn(AnalysisUnitController.class).getComponentDetail(analysisUnitId,
                            component.getComponentId(), null, cvssVersion)).withSelfRel()))
                .collect(Collectors.toList());

        return CollectionModel.of(resources, selfRefLinks);
    }

    @ApiOperation(value = "Get scan vulnerabilities for the passing scan",
        notes = "Returns ScanVulnerability information and links to associated resources.")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{analysis_unit_id}/scan:vulnerabilities")
    public CollectionModel<? extends LinkedCommonScanVulnerability> getVulnerabilities(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVE_ID, required = false) final String cveId,
            @ApiParam(value = LICENSE_NAME_INFO) @RequestParam(value = LICENSE_NAME,
                required = false) final String licenseName,
            @ApiParam(value = HAS_LICENSE_INFO) @RequestParam(value = HAS_LICENSE,
                required = false) final Boolean hasLicense,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES,
                required = false) final IssueSeverity[] severities,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = HAS_RISK_RATING_INFO) @RequestParam(value = HAS_RISK_RATING,
                required = false) final Boolean hasLicenseRiskRating,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS,
                required = false) final String[] riskRatings,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final Filter criteria = new Filter.FilterBuilder().cveId(cveId).fileName(fileName).severities(severities)
            .licenseName(licenseName).hasLicense(hasLicense).hasLicenseRiskRating(hasLicenseRiskRating)
            .licenseRiskRatings(riskRatings).cvssVersion(cvssVersion).createFilter();
        String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        final List<? extends LinkedCommonScanVulnerability> vulnerabilities =
            scanReportService.getLinkedScanVulnerabilities(scanId, getOrUseCurrentPolicy(policy), criteria);

        final List<Link> selfRefLinks = new LinkedList<>();
        selfRefLinks.add(linkTo(methodOn(ScanControllerV2.class).getVulnerabilities(scanId, fileName, cveId,
            licenseName, hasLicense, severities, null, hasLicenseRiskRating, riskRatings, cvssVersion)).withSelfRel());

        return CollectionModel.of(vulnerabilities, selfRefLinks);

    }

    @ApiOperation(value = "Promote SCA Scan", notes = "Returns promoted scan", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Promoted successfully", response = Scan.class)})
    @RequestMapping(value = "/{analysis_unit_id}/scan:promote", method = RequestMethod.PUT)
    public ResponseEntity<Scan> promoteScan(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = "Scan Request", required = true) @RequestBody final ScanPromoteRequest promoteRequest) {
        final Scan scan = scanService.getScanByAnalysisUnitIdIncludingDeleted(analysisUnitId);
        final Scan promoted = scanService.promoteScan(scan.getScanId(), promoteRequest);
        if (promoted == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } else {
            scanResultService.scanCompleted(promoted.getScanId(), null);
            scanResultService.updateProjectScan(promoted);
            return new ResponseEntity<>(promoted, HttpStatus.OK);
        }
    }

    @ApiOperation(value = "Share SCA Scan with another application")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Created successfully")})
    @PostMapping(value = "/{analysis_unit_id}/scan:share")
    public ResponseEntity<ShareScan> shareScan(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @Valid @RequestBody final ShareScanKey share) {
        ShareScan shared = null;
        final String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        try {
            shared = scanService.shareScan(ShareScanKey.init(scanId, share.getAppId(), share.getAccountId()));
        } catch (ScanResultProcessException e) {
            LOG.warn("Scan not found for the shareScan action.");
        }
        return new ResponseEntity<>(shared, HttpStatus.OK);
    }

    @ApiOperation(value = "Unshare SCA Scan with another application")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Unshared successfully")})
    @DeleteMapping(value = "/{analysis_unit_id}/scan:share")
    public ResponseEntity<String> unshareScan(
            @ApiParam(value = ANALYSIS_UNIT_ID,
                required = true) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = APP_INFO, example = "54321", required = true) @RequestParam(value = APP) final long appId,
            @ApiParam(value = ACCOUNT_INFO, example = "54321",
                required = true) @RequestParam(value = ACCOUNT) final long accountId) {
        final String scanId = scanService.getScanByAnalysisUnitId(analysisUnitId).getScanId();
        scanService.unshareScan(ShareScanKey.init(scanId, appId, accountId));
        return new ResponseEntity<>("Success", HttpStatus.OK);
    }

    @ApiOperation(value = "Retrieve library details found in the scan.")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping("/{analysis_unit_id}/scan:components/{component_id}")
    public ResponseEntity<ScanComponentModel> getComponentDetail(
            @ApiParam(value = ANALYSIS_UNIT_ID) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = COMPONENT_ID_INFO) @VVGuid @PathVariable(COMPONENT_ID) final String componentId,
            @ApiParam(value = POLICY) @RequestParam(value = "policy", required = false) final ScanPolicy policy,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final Scan scan = scanService.getScanByAnalysisUnitId(analysisUnitId);
        return Optional.ofNullable(scan)
            .map(s -> scanReportService.getLinkedScanComponentDetail(s.getScanId(), componentId,
                getOrUseCurrentPolicy(policy), cvssVersion))
            .map(component -> new ResponseEntity<>(component, HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "Retrieve vulnerabilities of the library found in the scan.")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping("/{analysis_unit_id}/scan:components/{component_id}/vulnerabilities")
    public CollectionModel<LinkedScanVulnerability> getVulnerabilitiesOfComponent(
            @ApiParam(value = ANALYSIS_UNIT_ID) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = COMPONENT_ID_INFO) @VVGuid @PathVariable(COMPONENT_ID) final String componentId,
            @ApiParam(value = POLICY) @RequestParam(value = "policy", required = false) final ScanPolicy policy,
            @ApiParam(value = CVSS_VERSION_TYPE) @RequestParam(value = "cvssType",
                required = false) final CvssVersion cvssVersion) {

        final Scan scan = Optional.ofNullable(scanService.getScanByAnalysisUnitId(analysisUnitId))
            .orElseThrow(ResourceNotFoundException  ::new);
        final List<LinkedScanVulnerability> componentVulns = scanReportService.getLinkedScanComponentVulnerabilities(
            scan.getScanId(), componentId, getOrUseCurrentPolicy(policy), cvssVersion);
        final List<Link> selfRefLinks = List.of(linkTo(methodOn(AnalysisUnitController.class)
            .getVulnerabilitiesOfComponent(analysisUnitId, componentId, null, cvssVersion)).withSelfRel());
        return CollectionModel.of(componentVulns, selfRefLinks);
    }

    @ApiOperation(value = "Get mitigation history of the library", response = Mitigation.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = Mitigation.class)})
    @GetMapping(value = "/{analysis_unit_id}/scan:components/{component_id}/mitigations")
    public CollectionModel<Mitigation> getMitigationHistoryOfComponent(
            @ApiParam(value = ANALYSIS_UNIT_ID) @Positive @PathVariable(AU_ID) final long analysisUnitId,
            @ApiParam(value = COMPONENT_ID_INFO,
                required = true) @VVGuid @PathVariable(COMPONENT_ID) final String componentId,
            @ApiParam(value = GENERATED_TS_INFO) @RequestParam(value = GENERATED_TS,
                required = false) final Timestamp generatedTs) {

        final Scan scan = Optional.ofNullable(scanService.getScanByAnalysisUnitId(analysisUnitId))
            .orElseThrow(ResourceNotFoundException::new);
        final List<Mitigation> mitigations =
            scanReportService.getMitigationHistory(scan.getScanId(), componentId, generatedTs);
        final List<Link> selfRefLinks = List.of(linkTo(methodOn(AnalysisUnitController.class)
            .getMitigationHistoryOfComponent(analysisUnitId, componentId, generatedTs)).withSelfRel());
        return CollectionModel.of(mitigations, selfRefLinks);
    }

    /**
     * get the policy from policy-backend or use the current policy.
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        ScanPolicy scanPolicy = Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
        // Use the policy name sent by VOSP, policy service does not send the name with response
        if (scanPolicy != null) {
            scanPolicy.setPolicyName(policy.getPolicyName());
        }
        return scanPolicy;
    }

    /**
     * Evaluate policy on given scan and returns policy compliance model
     * @param scanId
     * @param appId
     * @param policy
     * @param cvssVersion
     * @return
     */
    private PolicyCompliance getPolicyCompliance(String scanId, Long appId, ScanPolicy policy, CvssVersion cvssVersion,
            boolean savePolicyCompliance) {
        final PolicyComplianceModel model = policyService.evaluateScanPolicyCompliance(appId, scanId, policy, cvssVersion);
        final PolicyCompliance policyCompliance = policyService.saveOrGetPolicyCompliance(appId, scanId, model.getPolicyCompliance(),
                        savePolicyCompliance);
        LOG.info("Policy compliance request scanId - " + scanId + "grace period day - " + policyCompliance.getGraceTs());
        return policyCompliance;
    }
}
