package com.veracode.sca.scanresult.controller;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.alert.service.AlertService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * Application Alert controller.
 */
@Api(value = "SCA Application Alert API")
@RestController
@RequestMapping(value = {"/v1/applications", "/applications"}, produces = MediaType.APPLICATION_JSON_VALUE)
public class AppAlertController {

    private static final String ALERT_TS = "alert_ts";
    private static final String ALERT_TS_DESC = "Retrieve alerts from this timestamp.";
    private static final String ALERT_BATCH_SIZE = "batch_size";
    private static final String ALERT_BATCH_SIZE_DESC = "Number of applications having SCA alerts, if not passed in default value will be used.";

    private final AlertService alertService;

    @Autowired
    public AppAlertController(AlertService alertService) {
        this.alertService = alertService;
    }

    /**
     * Return alerted applications.
     *
     * @param alertTs   alert ts.
     * @param batchSize number of applications requested.
     * @return List of {application ids from alert table}.
     */
    @ApiOperation(value = "List (Batch) of Application ids having SCA alerts.",
        notes = "Returns applications requested",
        response = BigInteger.class)
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Successfully retrieved the list of application IDs.", response = BigInteger[].class)})
    @GetMapping(value = "/alerted")
    public ResponseEntity<List<BigInteger>> getAlertedApplications(
        @ApiParam(value = ALERT_BATCH_SIZE_DESC) @RequestParam(value = ALERT_BATCH_SIZE, required = false, defaultValue = "10") int batchSize,
        @ApiParam(value = ALERT_TS_DESC) @RequestParam(value = ALERT_TS, required = false) Timestamp alertTs) {

        if (alertTs == null) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_YEAR, -2);
            alertTs = new Timestamp(cal.getTimeInMillis());
        }

        final List<BigInteger> appIds = alertService.getAlertedApplications(batchSize, alertTs);
        if (appIds != null && appIds.size() > 0) {
            alertService.updateApplicationsAlerts(appIds, true);
        }
        return new ResponseEntity<>(appIds, HttpStatus.OK);
    }
}
