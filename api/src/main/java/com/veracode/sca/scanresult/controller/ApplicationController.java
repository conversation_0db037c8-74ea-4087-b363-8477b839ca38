package com.veracode.sca.scanresult.controller;

import com.veracode.sca.scanresult.alert.domain.Alert;
import com.veracode.sca.scanresult.alert.service.AlertService;
import com.veracode.sca.scanresult.domain.AppProject;
import com.veracode.sca.scanresult.domain.BulkMitigation;
import com.veracode.sca.scanresult.domain.BulkMitigationResult;
import com.veracode.sca.scanresult.domain.LatestScanDetails;
import com.veracode.sca.scanresult.domain.LinkedProject;
import com.veracode.sca.scanresult.domain.Mitigation;
import com.veracode.sca.scanresult.domain.MitigationReason;
import com.veracode.sca.scanresult.domain.MitigationStatus;
import com.veracode.sca.scanresult.platform.domain.Project;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import com.veracode.sca.scanresult.report.domain.AppMitigation;
import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.report.domain.Filter;
import com.veracode.sca.scanresult.report.domain.IssueSeverity;
import com.veracode.sca.scanresult.report.domain.MitigationType;
import com.veracode.sca.scanresult.report.service.AppMitigationReportService;
import com.veracode.sca.scanresult.service.ApplicationService;
import com.veracode.sca.scanresult.service.MitigationService;
import com.veracode.sca.scanresult.service.ProjectService;
import com.veracode.sca.scanresult.service.ScanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import com.google.common.annotations.VisibleForTesting;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Application controller.
 */
@Api(value = "SCA Application API")
@RestController
@RequestMapping(
    value = {"/v1/applications/{application}", "/applications/{application}"},
    produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ApplicationController {

    private static final String APPLICATION = "application";
    private static final String APPLICATION_DESC = "The application identifier.";
    private static final String ALERT_TS = "alert_ts";
    private static final String ALERT_TS_DESC = "Retrieve alerts from this timestamp, if not passed alerts for last two days will be returned.";
    private static final String POLICY = "policy";
    private static final String POLICY_DESC = "SCA policy";
    private static final String CVSS_VERSION_TYPE_INFO = "CVSS version type";
    private static final String CVSS_VERSION_TYPE = "cvssType";
    private static final String MITIGATION_TYPE_INFO = "Mitigation filter type, VULNERABILITY or LICENSE";
    private static final String MITIGATION_TYPE = "mitigationType";

    private final ScanService scanService;

    private final MitigationService mitigationService;

    private final AlertService alertService;

    private final ProjectService projectService;

    private final AppMitigationReportService reportService;

    @VisibleForTesting
    final PolicyComplianceService policyService;

    private final ApplicationService applicationService;

    @Autowired
    public ApplicationController(final ScanService scanService, final MitigationService mitigationService,
        final AlertService alertService, final ProjectService projectService,
        final AppMitigationReportService reportService, final PolicyComplianceService policyService,
                                 ApplicationService applicationService) {
        this.scanService = scanService;
        this.mitigationService = mitigationService;
        this.alertService = alertService;
        this.projectService = projectService;
        this.reportService = reportService;
        this.policyService = policyService;
        this.applicationService = applicationService;
    }

    /**
     * Delete SCA Scans of application.
     *
     * @param appId unique identifier of application.
     * @return number of scans deleted.
     */
    @ApiOperation(value = "Delete SCA Scans", notes = "Returns number of deleted scans")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Number of scans successfully deleted.", response = Integer.class),
        @ApiResponse(code = 404, message = "No scans found for the application")})
    @DeleteMapping(value = "/scans", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Integer> deleteScans(
        @ApiParam(value = APPLICATION_DESC, example = "54321") @PathVariable(APPLICATION) final long appId) {

        final int deletedScans = scanService.deleteScans(appId, true);
        return deletedScans == 0 ? new ResponseEntity<>(HttpStatus.NOT_FOUND)
            : new ResponseEntity<>(deletedScans, HttpStatus.OK);
    }

    /**
     * Create bulkMitigation
     *
     * @param appId          The application identifier
     * @param bulkMitigation the bulkMitigation
     * @return The list of mitigation
     */
    @ApiOperation(value = "Create Mitigation based on bulkMitigation",
        notes = "Returns Mitigation information and links to associated resources.")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Mitigations successfully created.", response = Mitigation[].class)})
    @PostMapping(value = "/mitigations")
    public ResponseEntity<BulkMitigationResult> createMitigations(
        @ApiParam(value = APPLICATION_DESC, example = "54321") @PathVariable(APPLICATION) final long appId,
        @ApiParam(value = "The bulk mitigation", required = true) @Valid @RequestBody final BulkMitigation bulkMitigation) {

        final BulkMitigationResult mitigations = mitigationService.bulkMitigate(appId, bulkMitigation.getAction(),
            bulkMitigation.getComments(), bulkMitigation.getMitigations());
        return new ResponseEntity<>(mitigations, HttpStatus.OK);
    }

    /**
     * Get ApplicationMitigation based on the ScanPolicy passed in
     *
     * @param appId            the application id
     * @param cveId            the cveId
     * @param cweId            the cweId
     * @param mitigationReason the mitigationReason
     * @param cveSeverities    the cveSeverities
     * @param policy           the policy
     * @return the {@link AppMitigation}
     */
    @ApiOperation(value = "Get ApplicationMitigation",
        notes = "Returns  ApplicationMitigation information and links to associated resources.")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Successfully retrieved list of mitigations.", response = AppMitigation[].class)})
    @GetMapping(value = "/mitigations")
    public CollectionModel<AppMitigation> getMitigations(
        @ApiParam(value = APPLICATION_DESC, example = "54321") @PathVariable(APPLICATION) final long appId,
        @ApiParam(value = "Cve identifier") @RequestParam(value = "cve_id", required = false) final String cveId,
        @ApiParam(value = "Cwe identifier") @RequestParam(value = "cwe_id", required = false) final String cweId,
        @ApiParam(value = "Mitigation reason") @RequestParam(value = "mitigation_reason", required = false) final MitigationReason mitigationReason,
        @ApiParam(value = MITIGATION_TYPE_INFO) @RequestParam(value = MITIGATION_TYPE, required = false) final MitigationType mitigationType,
        @ApiParam(value = "Mitigation status") @RequestParam(value = "mitigationStatus", required = false) final MitigationStatus mitigationStatus,
        @ApiParam(value = "Cve severity") @RequestParam(value = "severities", required = false) final IssueSeverity[] cveSeverities,
        @ApiParam(value = "Scan policy") @RequestParam(value = "policy", required = false) final ScanPolicy policy,
        @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE, required = false) final CvssVersion cvssVersion,
        @ApiParam(value = "License name") @RequestParam(value = "license_name", required = false) final String licenseName,
        @ApiParam(value = "License Risk Ratings") @RequestParam(value = "risk_ratings", required = false) final String[] licenseRisk) {

        final Filter filter = new Filter.FilterBuilder().cveId(cveId).cweId(cweId).mitigationReason(mitigationReason)
            .severities(cveSeverities).mitigationType(mitigationType).mitigationStatus(mitigationStatus)
            .licenseName(licenseName).licenseRiskRatings(licenseRisk).createFilter();
        final List<AppMitigation> appMitigations =
            reportService.getAppMitigation(appId, filter, getOrUseCurrentPolicy(policy), cvssVersion);

        final List<Link> selfRefLinks = new LinkedList<>();
        selfRefLinks
            .add(linkTo(methodOn(ApplicationController.class).getMitigations(appId, cveId, cweId, mitigationReason,
                mitigationType, mitigationStatus, cveSeverities, null, cvssVersion, licenseName, licenseRisk))
                    .withSelfRel());
        return CollectionModel.of(appMitigations, selfRefLinks);
    }

    /**
     * Return application alerts.
     *
     * @param appId   application id.
     * @param alertTs alert ts.
     * @param policy  sca policy.
     * @return List of {@link Alert}s.
     */
    @ApiOperation(value = "Return application alerts", notes = "Returns all the alerts for given application.",
        response = Alert.class)
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Successfully retrieve alerts", response = Alert[].class)})
    @RequestMapping(value = "/alerts", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public CollectionModel<Alert> getAppNotifications(
        @ApiParam(value = APPLICATION_DESC, example = "54321") @PathVariable(APPLICATION) final long appId,
        @ApiParam(value = ALERT_TS_DESC) @RequestParam(value = ALERT_TS, required = false) final Timestamp alertTs,
        @ApiParam(value = POLICY_DESC) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
        @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final List<Alert> alerts = alertService.getApplicationAlerts(appId, alertTs, getOrUseCurrentPolicy(policy), cvssVersion);

        Timestamp insertTs;
        if (alertTs == null) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_YEAR, -2);
            insertTs = new Timestamp(cal.getTimeInMillis());
        } else {
            insertTs = alertTs;
        }
        alertService.updateApplicationAlerts(appId, insertTs, true);

        final List<Link> selfRefLinks = new LinkedList<>();
        selfRefLinks
            .add(linkTo(methodOn(ApplicationController.class).getAppNotifications(appId, alertTs, null, cvssVersion)).withSelfRel());
        return CollectionModel.of(alerts, selfRefLinks);
    }

    /**
     * Create app-project
     *
     * @param applicationId     The application identifier
     * @param appProjectRequest the AppProject Request
     * @return The saved app-project information
     */
    @ApiOperation(value = "Save application - project linking information ")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Application link information saved successfully", response = AppProject.class)})
    @PostMapping(value = "/linkedproject")
    public ResponseEntity<AppProject> createAppProject(
        @ApiParam(value = APPLICATION_DESC, example = "54321") @PathVariable("application") final long applicationId,
        @ApiParam(value = "App Project Link Information Request",
            required = true) @Valid @RequestBody final AppProject appProjectRequest) {
        final AppProject appProject = projectService.linkProject(applicationId, appProjectRequest);
        return new ResponseEntity<>(appProject, HttpStatus.OK);
    }

    @ApiOperation(value = "Get Projects", notes = "Returns linked projects to a given application")
    @ApiResponses({@ApiResponse(code = 200, message = "Linked projects are successfully returned.",
        response = LinkedProject[].class)})
    @GetMapping(value = "/projects")
    public CollectionModel<EntityModel<LinkedProject>> getApplicationLinkedProjects(@ApiParam(value = APPLICATION_DESC,
        example = "54321") @PathVariable("application") final long applicationId) {

        List<Project> linkedProjects = projectService.getLinkedProjects(applicationId);
        if (linkedProjects.isEmpty()) {
            return CollectionModel.empty();
        }

        final List<EntityModel<LinkedProject>> resources = linkedProjects.stream()
            .map(project -> EntityModel.of(LinkedProject.init(project),
                Collections.singletonList(Link.of(project.getLinks().get("html").getHref(), "html"))))
            .collect(Collectors.toList());
        return CollectionModel.of(resources);
    }

    /**
     * Update application alerts.
     *
     * @param appId    application id.
     * @param insertTs alert to be updated.
     * @return number of alerts updated.
     */
    @ApiOperation(value = "Update application alerts", notes = "Update processed alerts of an application.")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Application alerts are successfully updated.", response = Integer.class)
    })
    @PutMapping(value = "/alerts/status")
    public ResponseEntity<Integer> updateApplicationAlertStatus(
            @ApiParam(value = APPLICATION_DESC, example = "54321") @PathVariable(APPLICATION) final long appId,
            @RequestBody Timestamp insertTs) {
        int recordUpdated = alertService.updateApplicationAlerts(appId, insertTs, false);
        return new ResponseEntity<>(recordUpdated, HttpStatus.OK);
    }

    /**
     * get the policy from policy-backend or use the current policy
     * @param policy
     * @return ScanPolicy
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        // TODO Fix Hack
        //   SCA database has Veracode Recommended Medium + SCA (22) in POLICY.policy_revisions.
        //   Other Veracode default policy is not present in the SCA database.
        //   The Org-id of this Policy is 0 which is causing issues with permission
        //   Don't get the policy if the sca policy is the ID is 22.
        if ( policy != null && policy.getPolicyId() == 22 ) {
            return policy;
        }
        return Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
    }

    @GetMapping("/latestscan")
    @ApiOperation(value = "Get latest scanId based on accountId and appId passed in",
            notes = "Returns the detail of latest scan",
            response = LatestScanDetails.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "You have successfully submitted your request.", response = LatestScanDetails.class),
            @ApiResponse(code = 400, message = "Invalid Request", response = LatestScanDetails.class),
            @ApiResponse(code = 401, message = "You are not authorized to perform this action."),
            @ApiResponse(code = 403, message = "Access denied. You are not authorized to make this request."),
            @ApiResponse(code = 404, message = "API not found. Verify the URI and try again."),
            @ApiResponse(code = 429, message = "Request limit exceeded. You have sent too many requests in a single time period. Submit your request again later."),
            @ApiResponse(code = 500, message = "Server-side error. Please try again later.")})
    public ResponseEntity<LatestScanDetails> getLatestScan(@ApiParam(value = APPLICATION_DESC, example = "4321")
                                                           @PathVariable(APPLICATION) final long appId) {
        final LatestScanDetails result = applicationService.getLatestScanInfo(appId);

        return result == null ? ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null)
                : new ResponseEntity<>(result, HttpStatus.OK);
    }

}
