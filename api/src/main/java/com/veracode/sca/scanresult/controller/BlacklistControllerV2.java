package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.util.Optional;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.domain.AccountBlacklist;
import com.veracode.sca.scanresult.domain.BlacklistRequest;
import com.veracode.sca.scanresult.report.domain.AccountBlacklistSummary;
import com.veracode.sca.scanresult.report.domain.BlocklistDetail;
import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.resources.BlocklistDetailResource;
import com.veracode.sca.scanresult.resources.BlocklistDetailResourceAssembler;
import com.veracode.sca.scanresult.service.BlacklistService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Api(value = "SCA Blacklist API v2")
@RestController
@RequestMapping(value = {"/v2/blacklist", "/v2/blocklist"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class BlacklistControllerV2 {

    private final BlacklistService blacklistService;
    private final BlocklistDetailResourceAssembler blocklistDetailResourceAssembler;

    @Value("${application.blacklist.result.page-size}")
    private int pageSize;

    /**
     * Construct a new instance
     *
     * @param blacklistService BlacklistService
     */
    @Autowired
    public BlacklistControllerV2(final BlacklistService blacklistService,
            final BlocklistDetailResourceAssembler blocklistDetailResourceAssembler) {
        this.blacklistService = blacklistService;
        this.blocklistDetailResourceAssembler = blocklistDetailResourceAssembler;
    }

    /**
     * Create {@link AccountBlacklist} with the blacklist passed in.
     * 
     * @param blacklist the {@link AccountBlacklist} request.
     * @return instance of {@link AccountBlacklist}.
     */
    @ApiOperation(value = "Blacklist the third party component",
        notes = "Returns details of blacklisted component with state changed status.",
        response = AccountBlacklist.class)
    @ApiResponses(
        value = {@ApiResponse(code = 200, message = "Component is blacklisted", response = AccountBlacklist.class),
            @ApiResponse(code = 400, message = "Invalid Request", response = AccountBlacklist.class)})
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    public ResponseEntity<AccountBlacklist> createBlacklist(@ApiParam(value = "The blacklist passed in",
        required = true) @RequestBody final AccountBlacklist blacklist) {
        final AccountBlacklist result = blacklistService.create(blacklist);
        return result == null ? ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null)
            : new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * Exposing endpoint to return the list of blocklisted components in the account.
     */
    @ApiOperation(value = "Get blacklists based on accountId passed in", notes = "Returns blacklists for the account ",
        response = AccountBlacklistSummary.class)
    @ApiResponses(
        value = {@ApiResponse(code = 200, message = "Success response", response = AccountBlacklistSummary.class)})
    @GetMapping
    public PagedModel<BlocklistDetailResource> getBlacklistV2(
            @ApiParam(value = "CVSS Version") @RequestParam(value = "cvssType",
                required = false) CvssVersion cvssVersion,
            Pageable pageable, final PagedResourcesAssembler<BlocklistDetail> assembler) {
        final Page<BlocklistDetail> result = blacklistService.getBlocklistComponents(cvssVersion, pageable);
        return assembler.toModel(result, blocklistDetailResourceAssembler);
    }

    /**
     * Get AccountBlacklist with the accountId passed in
     *
     * @param blacklistRequest portfolio request
     *
     * @return the {@link AccountBlacklistSummary}
     */
    @ApiOperation(value = "Get blacklists based on accountId passed in", notes = "Returns blacklists for the account ",
        response = AccountBlacklistSummary.class)
    @ApiResponses(
        value = {@ApiResponse(code = 200, message = "Success response", response = AccountBlacklistSummary.class)})
    @RequestMapping(value = "/result", method = RequestMethod.POST, produces = "application/json")
    public PagedModel<EntityModel<AccountBlacklistSummary>> getBlacklist(
            @ApiParam(value = "The latest scan request",
                required = true) @Valid @RequestBody final BlacklistRequest blacklistRequest,
            @ApiParam(value = "Page number") @RequestParam("page") final Optional<Integer> page,
            @ApiParam(value = "Page size") @RequestParam("size") final Optional<Integer> size,
            final PagedResourcesAssembler<AccountBlacklistSummary> assembler) {
        Pageable pageRequest =
            (blacklistRequest.getPage() != null || page.isPresent()) ? blacklistRequest.createPageRequest(page, size)
                : PageRequest.of(0, pageSize);
        final Page<AccountBlacklistSummary> result = blacklistService
            .getBlacklistWithCvssVersion(blacklistRequest.getAppIds(), blacklistRequest.getCvssVersion(), pageRequest);
        return assembler.toModel(result,
            linkTo(methodOn(BlacklistControllerV2.class).getBlacklist(blacklistRequest, page, size, assembler))
                .withSelfRel());
    }
}
