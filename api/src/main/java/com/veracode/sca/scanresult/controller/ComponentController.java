package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.util.List;
import java.util.Optional;

import javax.validation.Valid;

import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.PagedModel;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.domain.Component;
import com.veracode.sca.scanresult.domain.ComponentAppRequest;
import com.veracode.sca.scanresult.report.domain.ArtifactFilePortfolio;
import com.veracode.sca.scanresult.report.domain.ComponentCve;
import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.report.domain.LinkedAppPortfolio;
import com.veracode.sca.scanresult.report.service.ComponentReportService;
import com.veracode.security.validation.VVGuid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Api(value = "SCA Component API")
@RestController
@RequestMapping(value = {"/v1/components/{component}", "/components/{component}"},
    produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
public class ComponentController {

    private static final String COMPONENT = "component";
    private static final String COMPONENT_ID = "The component identifier";
    private static final String CVSS_VERSION_TYPE = "cvssType";
    private static final String CVSS_VERSION_TYPE_INFO =
        "CVSS version type, if not passed in cvss 2 scores will be returned";
    private static final String SUCCESS = "Success response";

    private final ComponentReportService reportService;
    private final PolicyComplianceService policyService;
    /**
     * Construct a new instance
     *
     * @param reportService the component reportService
     */
    @Autowired
    public ComponentController(final ComponentReportService reportService, final PolicyComplianceService policyService) {
        this.reportService = reportService;
        this.policyService = policyService;
    }

    /**
     * Get ComponentDetail for a component id
     *
     * @param componentId the component identifier.
     * @param cvssVersion cvss score version.
     * @return the Component
     */
    @ApiOperation(value = "Get Component detail by component identifier",
        notes = "Returns the Component detail of a component by component identifier")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping
    public EntityModel<Component> getComponentDetail(
            @ApiParam(value = COMPONENT_ID, required = true) @VVGuid @PathVariable(COMPONENT) final String componentId,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final Component componentDetail = reportService.getComponentDetail(componentId, cvssVersion);
        final List<Link> selfRefLinks = List
            .of(linkTo(methodOn(ComponentController.class).getComponentDetail(componentId, cvssVersion)).withSelfRel());
        return EntityModel.of(componentDetail, selfRefLinks);
    }

    /**
     * Get dependent applications for a component id
     *
     * @param componentAppRequest the componentAppRequest which includes componentId, and appIds
     * @return the {@link LinkedAppPortfolio}
     */
    @ApiOperation(value = "Get dependent applications by component identifier",
        notes = "Returns dependent applications information and links to associated resources by component identifier")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = LinkedAppPortfolio[].class)})
    @PostMapping(value = "/applications")
    public CollectionModel<LinkedAppPortfolio> getDependentApplications(
            @ApiParam(value = COMPONENT_ID) @VVGuid @PathVariable(COMPONENT) final String componentId,
            @ApiParam(value = "Component Request",
                required = true) @Valid @RequestBody final ComponentAppRequest componentAppRequest) {

        final List<LinkedAppPortfolio> dependentApps =
            reportService.getDependentApplications(componentId, componentAppRequest.getAppIds());
        final List<Link> selfRefLinks = List
            .of(linkTo(methodOn(ComponentController.class).getDependentApplications(componentId, componentAppRequest))
                .withSelfRel());
        return CollectionModel.of(dependentApps, selfRefLinks);
    }

    /**
     * Get component cve's for a component id
     *
     * @param componentId the componentId
     * @return the {@link ComponentCve}
     */
    @ApiOperation(value = "Get component common vulnerabilities and exposures (CVE) by component identifier",
        notes = "Returns component common vulnerabilities and exposures (CVE) information and links to associated resources by component identifier",
        response = ComponentCve.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = ComponentCve.class)})
    @GetMapping(value = "/vulnerabilities")
    public CollectionModel<ComponentCve> getComponentVulnerabilitiesReport(
            @ApiParam(value = COMPONENT_ID, required = true) @VVGuid @PathVariable(COMPONENT) final String componentId,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final List<ComponentCve> componentVulns = reportService.getVulnerabilities(componentId, cvssVersion);

        final List<Link> selfRefLinks = List
            .of(linkTo(methodOn(ComponentController.class).getComponentVulnerabilitiesReport(componentId, cvssVersion))
                .withSelfRel());
        return CollectionModel.of(componentVulns, selfRefLinks);
    }

    /**
     * Get list of other versions Components for a component id
     *
     * @param componentId the componentId
     * @param componentAppRequest the componentAppRequest which includes componentId, appIds, policy, and cvss version
     * @return {@link ArtifactFilePortfolio}
     */
    @ApiOperation(
        value = "Get other version components and their common vulnerabilities and exposures (CVE) summaries by component identifier",
        notes = "Returns other version components and common vulnerabilities and exposures (CVE) summaries and links to associated resources by component identifier")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = ArtifactFilePortfolio[].class)})
    @PostMapping(value = "/otherversions")
    public CollectionModel<ArtifactFilePortfolio> getComponentOtherVersionsReport(
            @ApiParam(value = COMPONENT_ID, required = true) @VVGuid @PathVariable(COMPONENT) final String componentId,
            @ApiParam(value = "ComponentApp Request",
                required = true) @Valid @RequestBody final ComponentAppRequest componentAppRequest) {

        final List<ArtifactFilePortfolio> artifactVulns = reportService.getOtherVersions(componentId,
            componentAppRequest.getAppIds(), getOrUseCurrentPolicy(componentAppRequest.getPolicy()),
            componentAppRequest.getCvssVersion());
        final List<Link> selfRefLinks = List.of(linkTo(
            methodOn(ComponentController.class).getComponentOtherVersionsReport(componentId, componentAppRequest))
                .withSelfRel());
        return CollectionModel.of(artifactVulns, selfRefLinks);
    }

    /**
     * Get page of other versions Components for a component id
     *
     * @param componentId the componentId
     * @param componentAppRequest the componentAppRequest which includes componentId, appIds, policy, and cvss version
     * @return {@link ArtifactFilePortfolio}
     */
    @ApiOperation(
            value = "Get paged other version components and their common vulnerabilities and exposures (CVE) summaries by component identifier",
            notes = "Returns paged other version components and common vulnerabilities and exposures (CVE) summaries and links to associated resources by component identifier")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = ArtifactFilePortfolio[].class)})
    @PostMapping(value = "/otherversions/paged")
    public PagedModel<EntityModel<ArtifactFilePortfolio>> getComponentOtherVersionsReport(
            @ApiParam(value = COMPONENT_ID, required = true) @VVGuid @PathVariable(COMPONENT) final String componentId,
            @ApiParam(value = "ComponentApp Request",
                    required = true) @Valid @RequestBody final ComponentAppRequest componentAppRequest,
            final PagedResourcesAssembler<ArtifactFilePortfolio> assembler) {

        final Page<ArtifactFilePortfolio> artifactVulns = reportService.getOtherVersions(componentId,
                componentAppRequest.getAppIds(), getOrUseCurrentPolicy(componentAppRequest.getPolicy()),
                componentAppRequest.getCvssVersion(), componentAppRequest.createPageRequest());

        return assembler.toModel(artifactVulns,
                linkTo(methodOn(ComponentController.class).getComponentOtherVersionsReport(componentId, componentAppRequest, assembler)).withSelfRel());
    }
    /**
     * get the policy from policy-backend or use the current policy
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        return Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
    }
}
