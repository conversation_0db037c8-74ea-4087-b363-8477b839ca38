package com.veracode.sca.scanresult.controller;

import java.io.IOException;
import java.util.NoSuchElementException;
import java.util.Set;

import javax.persistence.EntityNotFoundException;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.veracode.agora.identity.auth.Principal;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.util.UserPrincipal;
import com.veracode.security.logging.SecureLogger;

/**
 * Global exception handlers that apply across all controllers.
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    private static final SecureLogger LOG = SecureLogger.getLogger(GlobalExceptionHandler.class);

    private ObjectMapper jacksonObjectMapper;

    @Autowired
    public GlobalExceptionHandler(ObjectMapper objectMapper) {
        this.jacksonObjectMapper = objectMapper;
    }

    /**
     * Custom data binding
     *
     * @param binder WebDataBinder
     */
    @InitBinder
    void initBinder(final WebDataBinder binder) {
        binder.registerCustomEditor(ScanPolicy.class, new ScanPolicySupport(jacksonObjectMapper));
    }

    /**
     * REST error handler for access denied.
     *
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler({AccessDeniedException.class, NoSuchElementException.class})
    @ResponseStatus(HttpStatus.FORBIDDEN)
    void handleAccessDenied(final Exception e, final HttpServletResponse response) throws IOException {
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.error("Access is denied, from account " + UserPrincipal.getAccountId(user) + " login account "
                            + user.getUserId(),
                    e);
        } catch (Exception e2) {
            LOG.error("Access is denied, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.FORBIDDEN.value());
    }

    /**
     * REST error handler for bad request.
     *
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler({MethodArgumentTypeMismatchException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    void handleBadRequest(final Exception e, final HttpServletResponse response) throws IOException {
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.info("Bad Request from account " + UserPrincipal.getAccountId(
                    user) + ", login account " + user.getUserId(), e);
        } catch (Exception e2) {
            LOG.error("Bad Request from account, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.METHOD_NOT_ALLOWED.value());
    }

    /**
     * Log the ConstraintViolationException before returning.
     *
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler({ConstraintViolationException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    void handleException(final ConstraintViolationException e, final HttpServletResponse response) throws IOException {
        final Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        final StringBuilder strBuilder = new StringBuilder();
        for (final ConstraintViolation<?> violation : violations) {
            strBuilder.append(violation.getMessage());
            strBuilder.append('\n');
        }
        LOG.info("caught a ConstraintViolationException: " + strBuilder.toString());
        response.sendError(HttpStatus.BAD_REQUEST.value());
    }

    /**
     * Log the MethodArgumentNotValidException before returning.
     *
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    void handleMethodArgumentNotValid(final MethodArgumentNotValidException e, final HttpServletResponse response)
            throws IOException {
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.info("Bad Request from account " + UserPrincipal.getAccountId(
                    user) + ", login account " + user.getUserId(), e);
        } catch (Exception e2) {
            LOG.error("Bad Request from account, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.BAD_REQUEST.value());
    }

    /**
     * REST error handler for bad request.
     *
     * @param e
     * @param response
     * @throws IOException
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    void handleBadPaginationRequest(final Exception e, final HttpServletResponse response) throws IOException {
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.info("Bad Request from account " + UserPrincipal.getAccountId(
                    user) + ", login account " + +user.getUserId(), e);
        } catch (Exception e2) {
            LOG.error("Bad Request from account, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.BAD_REQUEST.value());
    }

    @ExceptionHandler(EntityNotFoundException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    void handleEntityNotFoundException(final Exception e, final HttpServletResponse response) throws IOException{
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.info("Bad Request from account " + UserPrincipal.getAccountId(
                    user) + ", login account " + +user.getUserId(), e.getMessage());
        } catch (Exception e2) {
            LOG.error("Bad Request from account, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.BAD_REQUEST.value());
    }

    @ExceptionHandler
    void handleMethodNotSupportedException(final HttpRequestMethodNotSupportedException e,
            final HttpServletResponse response) throws IOException {
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.error("Error: {}, request from the account {}, login account {}", e.getLocalizedMessage(),
                    UserPrincipal.getAccountId(user), user.getUserId());
        } catch (Exception e2) {
            LOG.error("Unexpected Exception for the account, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.METHOD_NOT_ALLOWED.value());
    }

    /**
     * REST error handler for internal error.
     */
    @ExceptionHandler
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    void handleException(final Exception e, final HttpServletResponse response) throws IOException {
        try {
            final Principal user = UserPrincipal.getPrincipal();
            LOG.error("Unexpected Exception: {} for the account {}, login account {}", e.getCause(),
                    UserPrincipal.getAccountId(user), user.getUserId(), e);
        } catch (Exception e2) {
            LOG.error("Unexpected Exception for the account, error while retrieving user principle", e2);
        }
        response.sendError(HttpStatus.INTERNAL_SERVER_ERROR.value());
    }

    /**
     * REST error handler for ResourceNotFoundException.
     */
    @ExceptionHandler
    @ResponseStatus(HttpStatus.NOT_FOUND)
    void handleException(final ResourceNotFoundException e, final HttpServletResponse response) throws IOException {
        LOG.warn("Unexpected ResourceNotFoundException", e);
        response.sendError(HttpStatus.NOT_FOUND.value());
    }
}
