package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.PagedModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.annotation.JsonView;
import com.veracode.sca.scanresult.domain.JsonViews;
import com.veracode.sca.scanresult.domain.PortfolioRequest;
import com.veracode.sca.scanresult.report.domain.ApplicationResult;
import com.veracode.sca.scanresult.report.domain.ComponentPortfolioResult;
import com.veracode.sca.scanresult.report.domain.LinkedAppPortfolioResult;
import com.veracode.sca.scanresult.report.domain.VulnerabilityPortfolioResult;
import com.veracode.sca.scanresult.report.service.PortfolioReportService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * SCA Portfolio API's
 */
@Api(value = "SCA Portfolio API")
@RestController
@RequestMapping(value = {"/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
public class PortfolioControllerV2 {

    public static final String SUCCESS = "Success response";

    private final PortfolioReportService reportService;

    /**
     * Construct a new instance
     *
     * @param reportService the reportService
     */
    @Autowired
    public PortfolioControllerV2(final PortfolioReportService reportService) {
        this.reportService = reportService;
    }

    /**
     * Get AppPortfolioResult based on the filter parameters passed in.
     *
     * @param portfolioRequest the portfolioRequest which includes appIds, criteria, pageable
     * @param assembler        the assembler
     * @return the list of AppPortfolioResult
     */
    @ApiOperation(value = "Get all applications for the portfolio", notes = "Returns all the applications")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS)})
    @PostMapping(value = "/applications")
    @JsonView({JsonViews.Portfolio.class})
    public PagedModel<EntityModel<LinkedAppPortfolioResult>> getApplications(
        @ApiParam("The portfolio request") @Valid @RequestBody final PortfolioRequest portfolioRequest,
        final PagedResourcesAssembler<LinkedAppPortfolioResult> assembler) {

        final Page<LinkedAppPortfolioResult> apps =
            reportService.getLinkedApplications(portfolioRequest.getAppIds(), portfolioRequest.getCvssVersion(),
                portfolioRequest.createPortfolioFilter(), portfolioRequest.createPageRequestForApplications());
        return assembler.toModel(apps,
            linkTo(methodOn(PortfolioControllerV2.class).getApplications(portfolioRequest, assembler)).withSelfRel());
    }

    /**
     * Get ComponentPortfolioResult based on the filter parameters passed in.
     *
     * @param portfolioRequest portfolioRequest
     * @param assembler        the assembler
     * @return list of ComponentPortfolioResult
     */
    @ApiOperation(value = "Get All components for the portfolio", notes = "Returns all the components")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS)})
    @PostMapping(value = "/components")
    @JsonView({JsonViews.Portfolio.class})
    public PagedModel<EntityModel<ComponentPortfolioResult>> getComponents(
        @ApiParam("The portfolio request") @Valid @RequestBody final PortfolioRequest portfolioRequest,
        final PagedResourcesAssembler<ComponentPortfolioResult> assembler) {

        final Page<ComponentPortfolioResult> components =
            reportService.getLinkedComponents(portfolioRequest.getAppIds(), portfolioRequest.getCvssVersion(),
                portfolioRequest.createPortfolioFilter(), portfolioRequest.createPageRequest());
        return assembler.toModel(components,
            linkTo(methodOn(PortfolioControllerV2.class).getComponents(portfolioRequest, assembler)).withSelfRel());
    }

    /**
     * Get VulnerabilityPortfolioResult
     *
     * @param portfolioRequest portfolioRequest
     * @return list of VulnerabilityPortfolioResult
     */
    @ApiOperation(value = "Get All vulnerabilities for the portfolio", notes = "Returns all the vulnerabilities")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS)})
    @PostMapping(value = "/vulnerabilities")
    @JsonView({JsonViews.Portfolio.class})
    public CollectionModel<VulnerabilityPortfolioResult> getVulnerabilities(
        @ApiParam("The portfolio request") @Valid @RequestBody final PortfolioRequest portfolioRequest) {

        final List<VulnerabilityPortfolioResult> vulnerabilities = reportService.getLinkedVulnerabilities(
            portfolioRequest.getAppIds(), portfolioRequest.getCvssVersion(), portfolioRequest.createPortfolioFilter());
        final List<Link> selfRefLinks = List.of(
            linkTo(methodOn(PortfolioControllerV2.class).getVulnerabilities(portfolioRequest)).withSelfRel());
        return CollectionModel.of(vulnerabilities, selfRefLinks);
    }

    /**
     * Get AppliactionResult
     *
     * @param portfolioRequest portfolioRequest
     * @param assembler        the assembler
     * @return the list of ApplicationResult
     */
    @ApiOperation(value = "Get all application result", notes = "Returns all application result for the account")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS, response = ApplicationResult[].class)})
    @PostMapping(value = "/applications/result")
    public PagedModel<EntityModel<ApplicationResult>> getResults(
        @ApiParam("The portfolio request") @Valid @RequestBody final PortfolioRequest portfolioRequest,
        final PagedResourcesAssembler<ApplicationResult> assembler) {

        final Page<ApplicationResult> appResults = reportService.getApplicationResult(portfolioRequest.getAppIds(),
            portfolioRequest.getCvssVersion(), portfolioRequest.createPageRequestForAppResult());
        return assembler.toModel(appResults,
            linkTo(methodOn(PortfolioControllerV2.class).getResults(portfolioRequest, assembler)).withSelfRel());
    }
}
