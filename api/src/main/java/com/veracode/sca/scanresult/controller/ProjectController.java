package com.veracode.sca.scanresult.controller;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.domain.DeleteProjects;
import com.veracode.sca.scanresult.service.ProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * Rest controller for projects.
 */
@Api(value = "SCA Projects API")
@RestController
@RequestMapping(value = {"/v1/projects", "/projects"}, produces = MediaType.APPLICATION_JSON_VALUE)
public class ProjectController {

    private final ProjectService projectService;

    /**
     * Constructor to initialize {@link ProjectController}.
     * 
     * @param projectService instance of {@link ProjectService}.
     */
    public ProjectController(final ProjectService projectService) {
        this.projectService = projectService;
    }

    /**
     * Update projects
     *
     * @param deleteProjects upload project request.
     * @return success message
     */
    @ApiOperation(value = "Delete projects by list of project ids", notes = "Delete projects by list of project ids")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "SUCCESS", response = String.class)})
    @PostMapping
    public ResponseEntity<String> updateProjects(
        @ApiParam(value = "DeleteProjects") @Valid @RequestBody final DeleteProjects deleteProjects) {
        projectService.deleteProjects(deleteProjects);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
