package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.domain.Mitigation;
import com.veracode.sca.scanresult.domain.ScanComponent;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import com.veracode.sca.scanresult.report.domain.ComponentSource;
import com.veracode.sca.scanresult.report.domain.ComponentSourceWrapper;
import com.veracode.sca.scanresult.report.domain.ScanVulnerability;
import com.veracode.sca.scanresult.report.service.ScanReportService;
import com.veracode.security.validation.VVGuid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * SCA Scan Component API
 */
@Api(value = "SCA Scan Component API")
@SuppressWarnings("PMD.AvoidDuplicateLiterals")
@RestController
@RequestMapping(
    value = {"/v1/scans/{scan}/components/{component}", "/scans/{scan}/components/{component}"},
    produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ScanComponentController {

    public static final String SCAN_ID = "The scan identifier";
    public static final String COMPONENT_ID = "The component identifier";
    public static final String POLICY = "The scan policy";
    public static final String SUCCESS = "Success response";
    public static final String APPLICATION_JSON = "application/json";
    public static final String GENERATED_TS = "Share report generated timestamp";
    public static final String GENERATEDTS = "generated_ts";
    private final ScanReportService reportService;
    private final PolicyComplianceService policyService;

    /**
     * Construct a new instance
     *
     * @param reportService the reportService
     */
    @Autowired
    public ScanComponentController(final ScanReportService reportService, final PolicyComplianceService policyService) {
        this.reportService = reportService;
        this.policyService = policyService;
    }

    /**
     * Get ScanComponent Detail for the scanId and component
     *
     * @param scanId      the scanId
     * @param componentId the componentId
     * @param policy      the policy
     * @return list of ScanComponent
     */
    @ApiOperation(value = "Get scan component details",
        notes = "Returns  scan component information and links to associated resources ")
    @ApiResponses({
        @ApiResponse(code = 200, message = SUCCESS, response = ScanComponent.class),
        @ApiResponse(code = 404, message = "EntityModel Not Found")})
    @GetMapping
    @Deprecated
    public ResponseEntity<ScanComponent> getComponentDetail(
        @ApiParam(value = SCAN_ID) @PathVariable("scan") final String scanId,
        @ApiParam(value = COMPONENT_ID) @VVGuid @PathVariable("component") final String componentId,
        @ApiParam(value = POLICY) @RequestParam(value = "policy", required = false) final ScanPolicy policy) {

        final ScanComponent componentDetail =
            reportService.getScanComponentDetail(scanId, componentId, getOrUseCurrentPolicy(policy));
        return componentDetail == null ? new ResponseEntity<>(HttpStatus.NOT_FOUND)
            : new ResponseEntity<>(componentDetail, HttpStatus.OK);
    }

    /**
     * Get ScanVulnerability for the specified scan and component
     *
     * @param scanId      the scanId
     * @param componentId the componentId
     * @param policy      the policy
     * @return list of ScanComponent
     */
    @ApiOperation(value = "Get scan vulnerabilities for the passing scan and component",
        notes = "Returns  ScanVulnerability information and links to associated resources ",
        response = ScanVulnerability.class)
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS, response = ScanVulnerability.class)})
    @GetMapping(value = "/vulnerabilities")
    @Deprecated
    public CollectionModel<ScanVulnerability> getScanComponentVulns(
        @ApiParam(value = SCAN_ID) @VVGuid @PathVariable("scan") final String scanId,
        @ApiParam(value = COMPONENT_ID) @VVGuid @PathVariable("component") final String componentId,
        @ApiParam(value = POLICY) @RequestParam(value = "policy", required = false) final ScanPolicy policy) {
        final List<ScanVulnerability> componentVulns =
            reportService.getScanComponentVulnerabilities(scanId, componentId, getOrUseCurrentPolicy(policy));
        final List<Link> selfRefLinks = List.of(linkTo(
            methodOn(ScanComponentController.class).getScanComponentVulns(scanId, componentId, null))
            .withSelfRel());
        return CollectionModel.of(componentVulns, selfRefLinks);
    }

    /**
     * Get ScanComponent Detail for the scanId and component
     *
     * @param scanId      the scanId
     * @param componentId the componentId
     * @param generatedTs the generatedTs
     * @return list of mitigation list
     */
    @ApiOperation(value = "Get Component Mitigation History", notes = "Returns  Component Mitigation History  ",
        response = Mitigation.class)
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS, response = Mitigation.class)})
    @GetMapping(value = "/mitigations")
    public CollectionModel<Mitigation> getMitigationHistory(
        @ApiParam(value = SCAN_ID) @VVGuid @PathVariable("scan") final String scanId,
        @ApiParam(value = COMPONENT_ID, required = true) @VVGuid @PathVariable("component") final String componentId,
        @ApiParam(value = GENERATED_TS) @RequestParam(value = GENERATEDTS, required = false) final Timestamp generatedTs) {

        final List<Mitigation> mitigations = reportService.getMitigationHistory(scanId, componentId, generatedTs);
        final List<Link> selfRefLinks = List.of(
            linkTo(methodOn(ScanComponentController.class).getMitigationHistory(scanId, componentId, generatedTs))
                .withSelfRel());
        return CollectionModel.of(mitigations, selfRefLinks);
    }

    /**
     * Get the sources that the component occurs.
     *
     * @param scanId      unique identifier of the scan.
     * @param componentId unique identifier of the component.
     * @return List of {@link ComponentSource}s.
     */
    @ApiOperation(value = "Get Component sources", notes = "Returns sources", response = ComponentSource.class)
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = SUCCESS, response = Map.class),
        @ApiResponse(code = 404, message = "Sources Not Found")})
    @GetMapping(value = "/sources")
    public ResponseEntity<Map.Entry<String, ComponentSourceWrapper>> getComponentSources(
        @ApiParam(value = SCAN_ID) @VVGuid @PathVariable("scan") final String scanId,
        @ApiParam(value = COMPONENT_ID) @VVGuid @PathVariable("component") final String componentId) {

        ComponentSourceWrapper sources = reportService.getScanComponentSources(scanId, componentId);
        return sources == null ? new ResponseEntity<>(HttpStatus.NOT_FOUND) : new ResponseEntity<>(
            Map.entry("_embedded", sources), HttpStatus.OK);
    }

    /**
     * get the policy from policy-backend or use the current policy
     * @param policy
     * @return ScanPolicy
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        return Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
    }

}
