package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.report.domain.LinkedScanComponent;
import com.veracode.sca.scanresult.report.domain.LinkedScanVulnerability;
import com.veracode.sca.scanresult.report.domain.ScanVulnerability;
import com.veracode.sca.scanresult.report.model.ScanComponentModel;
import com.veracode.sca.scanresult.report.service.ScanReportService;
import com.veracode.security.validation.VVGuid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Api(value = "SCA Scan Component API")
@RestController
@RequestMapping(
    value = {"/v2/scans/{scan}/components/{component}"},
    produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ScanComponentControllerV2 {

    public static final String APPLICATION_JSON = "application/json";
    private static final String SCAN_ID = "The scan identifier";
    private static final String COMPONENT_ID = "The component identifier";
    private static final String POLICY = "The scan policy";
    private static final String SUCCESS = "Success response";
    private static final String CVSS_VERSION_TYPE = "cvssType";
    private static final String CVSS_VERSION = "The CVSS Version type";

    private final ScanReportService reportService;
    private final PolicyComplianceService policyService;

    /**
     * Construct a new instance
     *
     * @param reportService the reportService
     */
    @Autowired
    public ScanComponentControllerV2(final ScanReportService reportService, final PolicyComplianceService policyService) {
        this.reportService = reportService;
        this.policyService = policyService;
    }

    /**
     * Get ScanComponent Detail for the scanId and component
     *
     * @param scanId      the scanId
     * @param componentId the componentId
     * @param policy      the policy
     * @return list of ScanComponent
     */
    @ApiOperation(value = "Get scan component details",
        notes = "Returns  scan component information and links to associated resources ")
    @ApiResponses({
        @ApiResponse(code = 200, message = SUCCESS, response = LinkedScanComponent.class),
        @ApiResponse(code = 404, message = "EntityModel Not Found")})
    @GetMapping
    public ResponseEntity<ScanComponentModel> getComponentDetail(
            @ApiParam(value = SCAN_ID) @VVGuid @PathVariable("scan") final String scanId,
            @ApiParam(value = COMPONENT_ID) @VVGuid @PathVariable("component") final String componentId,
            @ApiParam(value = POLICY) @RequestParam(value = "policy", required = false) final ScanPolicy policy,
            @ApiParam(value = CVSS_VERSION) @RequestParam(value = "cvssType",
                required = false) final CvssVersion cvssVersion) {

        return Optional
            .ofNullable(reportService.getLinkedScanComponentDetail(scanId, componentId, getOrUseCurrentPolicy(policy),
                cvssVersion))
            .map(component -> new ResponseEntity<>(component, HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * Get ScanVulnerability for the specified scan and component
     *
     * @param scanId      the scanId
     * @param componentId the componentId
     * @param policy      the policy
     * @return list of ScanComponent
     */
    @ApiOperation(value = "Get scan vulnerabilities for the passing scan and component",
        notes = "Returns  ScanVulnerability information and links to associated resources ",
        response = ScanVulnerability.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = LinkedScanVulnerability.class)})
    @GetMapping(value = "/vulnerabilities")
    public CollectionModel<LinkedScanVulnerability> getScanComponentVulns(
            @ApiParam(value = SCAN_ID) @VVGuid @PathVariable("scan") final String scanId,
            @ApiParam(value = COMPONENT_ID) @VVGuid @PathVariable("component") final String componentId,
            @ApiParam(value = POLICY) @RequestParam(value = "policy", required = false) final ScanPolicy policy,
            @ApiParam(value = CVSS_VERSION_TYPE) @RequestParam(value = "cvssType",
                required = false) final CvssVersion cvssVersion) {

        final List<LinkedScanVulnerability> componentVulns = reportService.getLinkedScanComponentVulnerabilities(scanId,
            componentId, getOrUseCurrentPolicy(policy), cvssVersion);
        final List<Link> selfRefLinks = List.of(linkTo(
            methodOn(ScanComponentControllerV2.class).getScanComponentVulns(scanId, componentId, null, cvssVersion))
                .withSelfRel());
        return CollectionModel.of(componentVulns, selfRefLinks);
    }

    /**
     * get the policy from policy-backend or use the current policy
     * @param policy the scanPolicy
     * @return ScanPolicy
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        return Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
    }
}
