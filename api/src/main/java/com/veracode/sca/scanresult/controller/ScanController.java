package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.annotation.JsonView;
import com.veracode.sca.scanresult.domain.AppScanUpdate;
import com.veracode.sca.scanresult.domain.JsonViews;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ScanComponent;
import com.veracode.sca.scanresult.domain.ScanPromoteRequest;
import com.veracode.sca.scanresult.domain.ScanRequest;
import com.veracode.sca.scanresult.domain.ScanResult;
import com.veracode.sca.scanresult.domain.ScanStatusUpdate;
import com.veracode.sca.scanresult.domain.ScanUpdate;
import com.veracode.sca.scanresult.policy.domain.PolicyCompliance;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import com.veracode.sca.scanresult.report.domain.Filter;
import com.veracode.sca.scanresult.report.domain.IssueSeverity;
import com.veracode.sca.scanresult.report.domain.ScanVulnerability;
import com.veracode.sca.scanresult.report.service.ScanReportService;
import com.veracode.sca.scanresult.service.ScanResultService;
import com.veracode.sca.scanresult.service.ScanService;
import com.veracode.security.logging.SecureLogger;
import com.veracode.security.validation.VVGuid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * 
 * SCA Scan API
 *
 */
@SuppressWarnings({"PMD.ExcessiveParameterList", "PMD.BeanMembersShouldSerializeRule", "PMD.AvoidDuplicateLiterals"})
@Api(value = "SCA scan API")
@RestController
@RequestMapping(value = {"/v1/scans", "/scans"}, produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ScanController {

    private static final SecureLogger LOG = SecureLogger.getLogger(ScanController.class);

    private static final String SCAN_ID = "The scan identifier";
    private static final String PREV_SCAN_ID = "Previous Scan Identifier";
    private static final String SCAN_POLICY = "The scan policy";
    private static final String FILENAME_INFO = "Component file name filter";
    private static final String CVE_INFO = "Common vulnerabilities and exposures (CVE) identifier filter";

    /** The Constant SEVERITIES_INFO. */
    private static final String SEVERITIES_INFO = "Severities filter";
    private static final String RISK_RATINGS_INFO = "Risk ratings filter";
    private static final String SUCCESS = "Success response";
    private static final String APPLICATION_JSON = "application/json";
    private static final String SCAN = "scan";
    private static final String PREV_SCAN = "prev_scan";
    private static final String FILENAME = "file_name";
    private static final String CVEID = "cve_id";
    private static final String SEVERITIES = "severities";
    private static final String HAS_RISK_RATING_INFO = "Has Risk Rating";
    private static final String HAS_RISK_RATING = "has_risk_rating";
    private static final String RISK_RATINGS = "risk_ratings";
    private static final String POLICY = "policy";
    private static final String APP = "app_id";
    private static final String APP_INFO = "The application identifier";
    private static final String LICENSENAME = "license_name";
    private static final String LICENSENAME_INFO = "License name filter";
    private static final String HASLICENSE = "has_license";
    private static final String HASLICENSE_INFO = "Has license filter";

    private final ScanReportService scanReportService;
    private final ScanService scanService;
    private final PolicyComplianceService policyService;
    private final ScanResultService scanResultService;


    /**
     * Construct a new instance
     * 
     * @param scanReportService the ScanService
     * @param policyService the PolicyComplianceService
     * @param scanService the scanService
     * @param scanResultService the scanResultService
     */
    @Autowired
    public ScanController(final ScanReportService scanReportService, final PolicyComplianceService policyService,
            final ScanService scanService, final ScanResultService scanResultService) {
        this.scanReportService = scanReportService;
        this.policyService = policyService;
        this.scanService = scanService;
        this.scanResultService = scanResultService;
    }

    /**
     * Create SCA Scan from scan request
     *
     * @param scanRequest the ScanRequest
     * @return Scan
     */
    @ApiOperation(value = "Create SCA Scan", notes = "Returns scan created", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Created successfully", response = Scan.class)})
    @RequestMapping(method = RequestMethod.POST)
    public ResponseEntity<Scan> createScan(
            @ApiParam(value = "Scan Request", required = true) @RequestBody final ScanRequest scanRequest) {
        final Scan scan = scanService.createScan(scanRequest);
        LOG.info("Created scan: " + scan + ", scanConfigId = " + scan.getScanConfigId() + ", scanId = " + scan.getScanId());
        return new ResponseEntity<>(scan, HttpStatus.OK);
    }

    /**
     * Return SCA scan, based on the given scan_id.
     *
     * @param scanId the scan identifier
     * @return Scan
     */
    @ApiOperation(value = "Get SCA Scan", notes = "Returns scan information", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Success response", response = Scan.class)})
    @RequestMapping(value = "/{scan_id}", method = RequestMethod.GET)
    public ResponseEntity<Scan> getScan(@ApiParam(value = "Scan identifier",
        required = true) @VVGuid @PathVariable("scan_id") final String scanId) {
        final Scan scan = scanService.getScan(scanId);
        return new ResponseEntity<>(scan, HttpStatus.OK);
    }

    /**
     * Update SCA Scan
     *
     * @param scanId Scan identifier
     * @param scanUpdate the ScanUpdate
     * @return Scan
     */
    @ApiOperation(value = "Update SCA Scan", notes = "Returns scan updated", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Updated successfully", response = Scan.class)})
    @RequestMapping(value = "/{scan_id}", method = RequestMethod.PUT)
    public ResponseEntity<Scan> updateScan(
            @ApiParam(value = "Scan identifier", required = true) @VVGuid @PathVariable("scan_id") final String scanId,
            @ApiParam(value = "Scan Update Request", required = true) @RequestBody final ScanUpdate scanUpdate) {
        LOG.info("scanUpdate - [scanId=" + scanId + ", sandboxId=" + scanUpdate.getSandboxId() + ", appVerId="
                + scanUpdate.getAppVerId() + ", analysisUnitId=" + scanUpdate.getAnalysisUnitId() + ", sandbox="
                + scanUpdate.getSandbox() + ", deleted=" + scanUpdate.getDeleted() + ", isPromoteScan="
                + scanUpdate.isPromoteScan() + "]");
        final Scan scan = scanService.undeleteScan(scanId, scanUpdate);
        if(scan == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(scan, HttpStatus.OK);
    }
    
    /**
     * Update SCA Scan
     *
     * @param scanId Scan identifier
     * @param statusUpdate the statusUpdate
     * @return Scan
     */
    @ApiOperation(value = "Update SCA Scan Status", notes = "Returns scan with updated status", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Updated successfully", response = Scan.class)})
    @RequestMapping(value = "/{scan_id}/status", method = RequestMethod.PUT)
    public ResponseEntity<Scan> updateScanStatus(
            @ApiParam(value = "Scan identifier", required = true) @VVGuid @PathVariable("scan_id") final String scanId,
            @ApiParam(value = "Scan Status", required = true) @RequestBody final ScanStatusUpdate statusUpdate) {
        LOG.info("updateScanStatus - [scanId=" + scanId + ", statusUpdate=" + statusUpdate + "]");
        final Scan scan = scanService.updateScanStatus(scanId, statusUpdate);
        return scan == null ? new ResponseEntity<>(HttpStatus.BAD_REQUEST) : new ResponseEntity<>(scan, HttpStatus.OK);
    }

    /**
     * Promote SCA scan, promote will create a new copy of the sandbox scan.
     *
     * @param scanId unique identifier of the scan to be promoted.
     * @param promoteRequest scan promote request.
     * @return promoted {@link Scan}.
     */
    @ApiOperation(value = "Promote SCA Scan", notes = "Returns promoted scan", response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Promoted successfully", response = Scan.class)})
    @RequestMapping(value = "/{scan_id}/promote", method = RequestMethod.PUT)
    public ResponseEntity<Scan> promoteScan(
            @ApiParam(value = "Scan identifier", required = true) @VVGuid @PathVariable("scan_id") final String scanId,
            @ApiParam(value = "Scan Request", required = true) @RequestBody final ScanPromoteRequest promoteRequest) {
        final Scan promoted = scanService.promoteScan(scanId, promoteRequest);
        if (promoted == null) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } else {
            scanResultService.scanCompleted(promoted.getScanId(), null);
            scanResultService.updateProjectScan(promoted);
            return new ResponseEntity<>(promoted, HttpStatus.OK);
        }
    }

    /**
     * Delete / undelete SCA Scans By AppId
     *
     * @param appScanUpdate appScanUpdate
     * @return int
     */
    @ApiOperation(value = "Delete SCA Scans", notes = "Returns number of deleted scans", response = Integer.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Deleted successfully", response = Integer.class)})
    @RequestMapping(value = "/deleteScans", method = RequestMethod.PUT)
    public ResponseEntity<Integer> deleteScans(
            @ApiParam(value = "Scan Request", required = true) @RequestBody final AppScanUpdate appScanUpdate) {
        LOG.info("deleteScans - deleteScanRequest: [appIds=" + appScanUpdate.getAppId() + ", delete="
                + appScanUpdate.getDelete() + "]");
        final int deletedScans = scanService.deleteScans(appScanUpdate.getAppId(), appScanUpdate.getDelete());
        return new ResponseEntity<>(deletedScans, HttpStatus.OK);
    }

    /**
     * Get ScanComponent details for the scan based on the scan, policy and filter parameters.
     *
     * @param scanId the scanId
     * @param prevScanId the prev scan id
     * @param policy the policy
     * @return list of ScanComponent details
     */
    @ApiOperation(value = "Get scan components for the passing scan",
        notes = "Returns ScanComponent information and links to associated resources ", response = ScanComponent.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = ScanComponent.class)})
    @RequestMapping(value = "/{scan}/result", method = RequestMethod.GET, produces = APPLICATION_JSON)
    @JsonView(JsonViews.ComponentDetail.class)
    @Deprecated
    public ResponseEntity<ScanResult> getResults(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = PREV_SCAN_ID) @RequestParam(value = PREV_SCAN, required = false) final String prevScanId,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy) {
        final ScanResult result = scanReportService.getScanResults(scanId, prevScanId, getOrUseCurrentPolicy(policy));
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * Get ScanVulnerabilities based on the scan, policy and filter parameters.
     *
     * @param scanId the scanId
     * @param fileName the fileName
     * @param cveId the cveId
     * @param licenseName the licenseName
     * @param hasLicense the hasLicense
     * @param severities the severities
     * @param policy the policy
     * @param hasLicenseRiskRating the has license risk rating
     * @param riskRatings the risk ratings
     * @return list of ScanVulnerability
     */
    @SuppressWarnings("PMD.ExcessiveParameterList")
    @ApiOperation(value = "Get scan vulnerabilities for the passing scan",
        notes = "Returns ScanVulnerability information and links to associated resources ",
        response = ScanVulnerability.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = ScanVulnerability.class)})
    @RequestMapping(value = "/{scan}/vulnerabilities", method = RequestMethod.GET, produces = APPLICATION_JSON)
    @Deprecated
    public CollectionModel<ScanVulnerability> getVulnerabilities(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVEID, required = false) final String cveId,
            @ApiParam(value = LICENSENAME_INFO) @RequestParam(value = LICENSENAME,
                required = false) final String licenseName,
            @ApiParam(value = HASLICENSE_INFO) @RequestParam(value = HASLICENSE,
                required = false) final Boolean hasLicense,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES,
                required = false) final IssueSeverity[] severities,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = HAS_RISK_RATING_INFO) @RequestParam(value = HAS_RISK_RATING,
                required = false) final Boolean hasLicenseRiskRating,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS,
                required = false) final String[] riskRatings) {

        final Filter criteria = new Filter.FilterBuilder().cveId(cveId).fileName(fileName).severities(severities)
            .licenseName(licenseName).hasLicense(hasLicense).hasLicenseRiskRating(hasLicenseRiskRating)
            .licenseRiskRatings(riskRatings).createFilter();
        final List<ScanVulnerability> vulnerabilities =
            scanReportService.getScanVulnerabilities(scanId, getOrUseCurrentPolicy(policy), criteria);

        final List<Link> selfRefLinks =
            List.of(linkTo(methodOn(ScanController.class).getVulnerabilities(scanId, fileName, cveId, licenseName,
                hasLicense, severities, null, hasLicenseRiskRating, riskRatings)).withSelfRel());

        return CollectionModel.of(vulnerabilities, selfRefLinks);
    }

    /**
     * Get policyCompliance based on the scan and scanPolicy passed in
     * 
     * @param scanId the scanId
     * @param appId the appId
     * @param policy the policy
     * @return PolicyCompliance
     */
    @ApiOperation(value = "Get policy compliance for the passing scan",
        notes = "Returns PolicyCompliance information and links to associated resources ",
        response = PolicyCompliance.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = PolicyCompliance.class)})
    @RequestMapping(value = "/{scan}/policycompliance", method = RequestMethod.GET, produces = APPLICATION_JSON)
    @Deprecated
    public PolicyCompliance getPolicycompliance(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = APP_INFO) @RequestParam(value = APP, required = false) final Long appId,
            @ApiParam(value = SCAN_POLICY, required = true) @RequestParam(value = POLICY) final ScanPolicy policy) {
        return policyService.evaluate(appId, scanId, getOrUseCurrentPolicy(policy));
    }

    /**
     * get the policy from policy-backend or use the current policy
     * @param policy
     * @return ScanPolicy
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        return Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
    }
}
