package com.veracode.sca.scanresult.controller;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.Link;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.veracode.sca.scanresult.domain.JsonViews;
import com.veracode.sca.scanresult.domain.LinkedScanResult;
import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.policy.PolicyComplianceModel;
import com.veracode.sca.scanresult.policy.domain.PolicyCompliance;
import com.veracode.sca.scanresult.policy.domain.PolicyComplianceStatus;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.sca.scanresult.policy.service.PolicyComplianceService;
import com.veracode.sca.scanresult.report.domain.CvssVersion;
import com.veracode.sca.scanresult.report.domain.Filter;
import com.veracode.sca.scanresult.report.domain.IssueSeverity;
import com.veracode.sca.scanresult.report.domain.LinkedCommonScanVulnerability;
import com.veracode.sca.scanresult.report.model.ScanComponentModel;
import com.veracode.sca.scanresult.report.model.ScanLicenseModel;
import com.veracode.sca.scanresult.report.service.ScanReportService;
import com.veracode.security.logging.SecureLogger;
import com.veracode.security.validation.VVGuid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Api(value = "SCA scan API")
@RestController
@RequestMapping(value = {"/v2/scans"}, produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ScanControllerV2 {

    private static final SecureLogger LOG = SecureLogger.getLogger(ScanControllerV2.class);

    private static final String SCAN_ID = "The scan identifier";
    private static final String PREV_SCAN_ID = "Previous Scan Identifier";
    private static final String SCAN_POLICY = "The scan policy";
    private static final String FILENAME_INFO = "Component file name filter";
    private static final String CVE_INFO = "Common vulnerabilities and exposures (CVE) identifier filter";
    private static final String CVSS_VERSION_TYPE_INFO = "CVSS Score version type, if not passed in CVSS2 will be returned.";
    private static final String CVSS_VERSION_TYPE = "cvssType";
    private static final String FIELDS_INFO = "Scan object fields filter";
    private static final String FIELDS = "fields";

    /** The Constant SEVERITIES_INFO. */
    private static final String SEVERITIES_INFO = "Severities filter";
    private static final String RISK_RATINGS_INFO = "Risk ratings filter";
    private static final String SUCCESS = "Success response";
    private static final String SCAN = "scan";
    private static final String PREV_SCAN = "prev_scan";
    private static final String FILENAME = "file_name";
    private static final String CVEID = "cve_id";
    private static final String SEVERITIES = "severities";
    private static final String HAS_RISK_RATING_INFO = "Has Risk Rating";
    private static final String HAS_RISK_RATING = "has_risk_rating";
    private static final String RISK_RATINGS = "risk_ratings";
    private static final String POLICY = "policy";
    private static final String APP = "app_id";
    private static final String APP_INFO = "The application identifier";
    private static final String LICENSENAME = "license_name";
    private static final String LICENSENAME_INFO = "License name filter";
    private static final String HASLICENSE = "has_license";
    private static final String HASLICENSE_INFO = "Has license filter";

    private final ScanReportService scanReportService;
    private final PolicyComplianceService policyService;

    /**
     * Construct a new instance
     *
     * @param scanReportService the ScanService
     * @param policyService the PolicyComplianceService
     */
    @Autowired
    public ScanControllerV2(final ScanReportService scanReportService, final PolicyComplianceService policyService) {
        this.scanReportService = scanReportService;
        this.policyService = policyService;
    }

    /**
     * Get ScanComponent based on the scan, policy and filter parameters.
     *
     * @param scanId the scanId
     * @param fileName the fileName
     * @param cveId the cveId
     * @param severities the severity
     * @param licenseName the licenseName
     * @param hasLicense the hasLicense
     * @param policy the policy
     * @param hasLicenseRiskRating the has license risk rating
     * @param riskRatings the risk ratings
     * @return list of ScanComponent
     */
    @ApiOperation(value = "Get scan components for the passing scan",
        notes = "Returns ScanComponent information and links to associated resources ")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{scan}/components")
    @JsonView({JsonViews.ComponentSummary.class})
    public CollectionModel<EntityModel<ScanComponentModel>> getComponents(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVEID, required = false) final String cveId,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES, required = false) final IssueSeverity[] severities,
            @ApiParam(value = LICENSENAME_INFO) @RequestParam(value = LICENSENAME, required = false) final String licenseName,
            @ApiParam(value = HASLICENSE_INFO) @RequestParam(value = HASLICENSE, required = false) final Boolean hasLicense,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = HAS_RISK_RATING_INFO) @RequestParam(value = HAS_RISK_RATING, required = false) final Boolean hasLicenseRiskRating,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS, required = false) final String[] riskRatings,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE, required = false) final CvssVersion cvssVersion) {

        final Filter criteria = new Filter.FilterBuilder().cveId(cveId).fileName(fileName).severities(severities)
            .licenseName(licenseName).hasLicense(hasLicense).hasLicenseRiskRating(hasLicenseRiskRating)
            .licenseRiskRatings(riskRatings).cvssVersion(cvssVersion).createFilter();
        final List<ScanComponentModel> components =
            scanReportService.getLinkedScanComponents(scanId, getOrUseCurrentPolicy(policy), criteria);

        final List<Link> selfRefLinks = Collections
            .singletonList(linkTo(methodOn(ScanControllerV2.class).getComponents(scanId, fileName, cveId, severities,
                licenseName, hasLicense, null, hasLicenseRiskRating, riskRatings, cvssVersion)).withSelfRel());

        List<EntityModel<ScanComponentModel>> resources =
            components.stream()
                .map(
                    component -> EntityModel.of(component,
                        linkTo(methodOn(ScanComponentControllerV2.class).getComponentDetail(component.getScanId(),
                            component.getComponentId(), null, cvssVersion)).withSelfRel()))
                .collect(Collectors.toList());

        return CollectionModel.of(resources, selfRefLinks);
    }

    /**
     * Get ScanComponent details for the scan based on the scan, policy and filter parameters.
     *
     * @param scanId the scanId
     * @param prevScanId the prev scan id
     * @param policy the policy
     * @return list of ScanComponent details
     */
    @ApiOperation(value = "Get scan components for the passing scan",
        notes = "Returns ScanComponent information and links to associated resources ")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{scan}/result")
    @JsonView(JsonViews.ComponentDetail.class)
    public ResponseEntity<LinkedScanResult> getResults(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = PREV_SCAN_ID) @VVGuid @RequestParam(value = PREV_SCAN, required = false) final String prevScanId,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) CvssVersion cvssVersion) {
        final LinkedScanResult result =
            scanReportService.getLinkedScanResults(scanId, prevScanId, getOrUseCurrentPolicy(policy), cvssVersion);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * Get ScanVulnerabilities based on the scan, policy and filter parameters.
     *
     * @param scanId the scanId
     * @param fileName the fileName
     * @param cveId the cveId
     * @param licenseName the licenseName
     * @param hasLicense the hasLicense
     * @param severities the severities
     * @param policy the policy
     * @param hasLicenseRiskRating the has license risk rating
     * @param riskRatings the risk ratings
     * @return list of ScanVulnerability
     */
    @SuppressWarnings("PMD.ExcessiveParameterList")
    @ApiOperation(value = "Get scan vulnerabilities for the passing scan",
        notes = "Returns ScanVulnerability information and links to associated resources.")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{scan}/vulnerabilities")
    public CollectionModel<? extends LinkedCommonScanVulnerability> getVulnerabilities(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVEID, required = false) final String cveId,
            @ApiParam(value = LICENSENAME_INFO) @RequestParam(value = LICENSENAME, required = false) final String licenseName,
            @ApiParam(value = HASLICENSE_INFO) @RequestParam(value = HASLICENSE, required = false) final Boolean hasLicense,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES, required = false) final IssueSeverity[] severities,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = HAS_RISK_RATING_INFO) @RequestParam(value = HAS_RISK_RATING, required = false) final Boolean hasLicenseRiskRating,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS, required = false) final String[] riskRatings,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE, required = false) final CvssVersion cvssVersion) {

        final Filter criteria = new Filter.FilterBuilder().cveId(cveId).fileName(fileName).severities(severities)
            .licenseName(licenseName).hasLicense(hasLicense).hasLicenseRiskRating(hasLicenseRiskRating)
            .licenseRiskRatings(riskRatings).cvssVersion(cvssVersion).createFilter();
        final List<? extends LinkedCommonScanVulnerability> vulnerabilities =
            scanReportService.getLinkedScanVulnerabilities(scanId, getOrUseCurrentPolicy(policy), criteria);

        final List<Link> selfRefLinks =
            List.of(linkTo(methodOn(ScanControllerV2.class).getVulnerabilities(scanId, fileName, cveId, licenseName,
                hasLicense, severities, null, hasLicenseRiskRating, riskRatings, cvssVersion)).withSelfRel());

        return CollectionModel.of(vulnerabilities, selfRefLinks);
    }

    @ApiOperation(value = "Get scan licenses for the passing scan",
            notes = "Returns ScanLicenses information and links to associated resources.")
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS)})
    @GetMapping(value = "/{scan}/licenses")
    public CollectionModel<ScanLicenseModel> getLicenses(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = FILENAME_INFO) @RequestParam(value = FILENAME, required = false) final String fileName,
            @ApiParam(value = CVE_INFO) @RequestParam(value = CVEID, required = false) final String cveId,
            @ApiParam(value = LICENSENAME_INFO) @RequestParam(value = LICENSENAME, required = false) final String licenseName,
            @ApiParam(value = SCAN_POLICY) @RequestParam(value = POLICY, required = false) final ScanPolicy policy,
            @ApiParam(value = SEVERITIES_INFO) @RequestParam(value = SEVERITIES, required = false) final IssueSeverity[] severities,
            @ApiParam(value = RISK_RATINGS_INFO) @RequestParam(value = RISK_RATINGS, required = false) final String[] licenseRiskRatings,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE,
                required = false) final CvssVersion cvssVersion) {

        final Filter criteria = new Filter.FilterBuilder().fileName(fileName).cveId(cveId).licenseName(licenseName)
            .severities(severities).licenseRiskRatings(licenseRiskRatings).cvssVersion(cvssVersion).createFilter();
        final List<ScanLicenseModel> licenses =
            scanReportService.getLinkedScanLicenses(scanId, getOrUseCurrentPolicy(policy), criteria);

        return CollectionModel.of(licenses);
    }

    /**
     * Get policyCompliance based on the scan and scanPolicy passed in
     *
     * @param scanId the scanId
     * @param appId the appId
     * @param policy the policy
     * @return PolicyCompliance
     */
    @ApiOperation(value = "Get policy compliance for the passing scan",
        notes = "Returns PolicyCompliance information and links to associated resources ",
        response = PolicyCompliance.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = PolicyCompliance.class)})
    @GetMapping(value = "/{scan}/policycompliance")
    public PolicyCompliance getPolicycompliance(
            @ApiParam(value = SCAN_ID, required = true) @VVGuid @PathVariable(SCAN) final String scanId,
            @ApiParam(value = APP_INFO) @RequestParam(value = APP, required = false) final Long appId,
            @ApiParam(value = CVSS_VERSION_TYPE_INFO) @RequestParam(value = CVSS_VERSION_TYPE, required = false) final CvssVersion cvssVersion,
            @ApiParam(value = SCAN_POLICY, required = true) @RequestParam(value = POLICY) final ScanPolicy policy) {

        final PolicyComplianceModel model =
            policyService.evaluateScanPolicyCompliance(appId, scanId, getOrUseCurrentPolicy(policy), cvssVersion);

        final PolicyCompliance policyCompliance = policyService.saveOrGetPolicyCompliance(appId, scanId, model.getPolicyCompliance());

        // workaround to fix NPE (SCALK-279) in VODP when the grace period is null, cannot set this at the model
        // creation since it will affect other API responses as well. i.e. scans/{}/components.
        // TODO: remove after vosp change is released.
        if (policyCompliance.getPolicyStatus() == PolicyComplianceStatus.DID_NOT_PASS
                && policyCompliance.getGraceTs() == null) {
            policyCompliance.setGraceTs(new Date());
        }
        return policyCompliance;
    }

    /**
     * Get the latest completed scan, returns all fields by default
     *
     * @param fields optional list of scan object fields to be returned
     * @return Scan
     */
    @ApiOperation(value = "Get latest completed scan",
        notes = "Returns Scan",
        response = Scan.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = SUCCESS, response = Scan.class)})
    @GetMapping(value = "/lastcompletedscan")
    public ResponseEntity<Object> getLatestCompletedScan(
            @ApiParam(value = FIELDS_INFO) @RequestParam(value = FIELDS, required = false) String fields) {

        Scan latestScan = scanReportService.latestCompletedScan();
        if (fields != null) {
            @JsonFilter("scanFilter")
            class ScanMixin {
            }

            ObjectMapper mapper = new ObjectMapper().addMixIn(Scan.class, ScanMixin.class);
            mapper.setFilterProvider(new SimpleFilterProvider().setFailOnUnknownId(false)
                .addFilter("scanFilter", SimpleBeanPropertyFilter.filterOutAllExcept(fields.split(","))));

            Map<String, Object> map = null;
            try {
                String json = mapper.writeValueAsString(latestScan);
                map = mapper.readValue(json, Map.class);
                LOG.debug("getLatestCompletedScan - fields=" + map.keySet());
            } catch (IOException e) {
                LOG.warn("getLatestCompletedScan - Unable to create partial scan object:\n" + e);
            }
            return new ResponseEntity<>(map, HttpStatus.OK);
        } else {
            LOG.debug("getLatestCompletedScan - optional \"fields\" query parameter not found");
            return new ResponseEntity<>(latestScan, HttpStatus.OK);
        }
    }

    /**
     * get the policy from policy-backend or use the current policy
     */
    private ScanPolicy getOrUseCurrentPolicy(final ScanPolicy policy) {
        return Optional.ofNullable(policy).map(p -> policyService.getPolicyDetails(p.getPolicyId())).orElse(policy);
    }
}
