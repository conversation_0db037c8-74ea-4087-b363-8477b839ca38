package com.veracode.sca.scanresult.controller;

import java.beans.PropertyEditorSupport;
import java.io.IOException;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.veracode.sca.scanresult.policy.domain.ScanPolicy;
import com.veracode.security.logging.SecureLogger;

/**
 * Transform policy query param to ScanPolicy
 *
 */
public class ScanPolicySupport extends PropertyEditorSupport {
    private static final SecureLogger LOG = SecureLogger.getLogger(ScanPolicySupport.class);

    private final ObjectMapper jacksonObjectMapper;

    /**
     * Construct ScanPolicySupport
     * 
     * @param jacksonObjectMapper ObjectMapper
     */
    public ScanPolicySupport(final ObjectMapper jacksonObjectMapper) {
        this.jacksonObjectMapper = jacksonObjectMapper;
    }

    /**
     * (non-Javadoc)
     * 
     * @see java.beans.PropertyEditorSupport#setAsText(java.lang.String)
     */
    @SuppressWarnings({"PMD.CommentRequired"})
    @Override
    public void setAsText(final String text) throws IllegalArgumentException {
        ScanPolicy policy = null;

        if (text != null) {
            try {
                policy = jacksonObjectMapper.readValue(text, ScanPolicy.class);
            } catch (final IOException e) {
                LOG.warn("Unabled to parse policy input: " + text);
            }
        }
        setValue(policy);
    }
}
