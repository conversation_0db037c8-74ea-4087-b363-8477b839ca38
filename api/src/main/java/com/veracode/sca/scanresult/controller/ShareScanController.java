package com.veracode.sca.scanresult.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.domain.ShareScan;
import com.veracode.sca.scanresult.domain.ShareScanKey;
import com.veracode.sca.scanresult.exception.ScanResultException;
import com.veracode.sca.scanresult.exception.ScanResultProcessException;
import com.veracode.sca.scanresult.service.ScanService;
import com.veracode.security.logging.SecureLogger;
import com.veracode.security.validation.VVGuid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * Rest controller responsible for sharing/unsharing {@link Scan} with other application and account
 *
 * <AUTHOR>
 */
@SuppressWarnings({"PMD.BeanMembersShouldSerializeRule", "PMD.DataflowAnomalyAnalysisRule"})
@Api(value = "Share Scan APIs")
@RestController
@RequestMapping(value = {"/v1/scans/{scan}/shares", "/scans/{scan}/shares"},
    produces = {MediaType.APPLICATION_JSON_VALUE})
@Validated
public class ShareScanController {

    private static final SecureLogger LOG = SecureLogger.getLogger(ShareScanController.class);
    private final ScanService scanService;

    /**
     * Default constructor
     *
     * @param scanService the ScanService
     */
    @Autowired
    public ShareScanController(final ScanService scanService) {
        this.scanService = scanService;
    }

    /**
     * Share SCA Scan with other application and account
     *
     * @param scanId Scan identifier
     * @param share  To share
     * @return ShareScan
     * @throws ScanResultException when ScanResultException occurs
     */
    @ApiOperation(value = "Share SCA Scan with another application")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Created successfully")})
    @PostMapping
    public ResponseEntity<ShareScan> shareScan(
        @ApiParam(value = "Scan identifier (UUID)", required = true) @VVGuid @PathVariable("scan") final String scanId,
        @Valid @RequestBody final ShareScanKey share) {
        ShareScan shared = null;
        try {
            shared = scanService.shareScan(ShareScanKey.init(scanId, share.getAppId(), share.getAccountId()));
        } catch (ScanResultProcessException e) {
            LOG.warn("Scan not found for the shareScan action.");
        }
        return new ResponseEntity<>(shared, HttpStatus.OK);
    }

    /**
     * Unshare SCA Scan with other application and account
     *
     * @param scanId    Scan identifier
     * @param appId     Application identifier
     * @param accountId Account identifier
     * @return ResponseEntity
     */
    @ApiOperation(value = "Unshare SCA Scan with another application")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "Unshared successfully")})
    @DeleteMapping
    public ResponseEntity<String> unshareScan(
        @ApiParam(value = "Scan Identifier (UUID)", required = true) @VVGuid @PathVariable("scan") final String scanId,
        @ApiParam(value = "Application identifier", example = "54321", required = true) @RequestParam(value = "app_id") final long appId,
        @ApiParam(value = "Account identifier", example = "54321", required = true) @RequestParam(value = "account_id") final long accountId) {
        scanService.unshareScan(ShareScanKey.init(scanId, appId, accountId));
        return new ResponseEntity<>("Success", HttpStatus.OK);
    }
}
