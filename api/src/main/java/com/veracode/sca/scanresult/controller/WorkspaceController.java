package com.veracode.sca.scanresult.controller;

import com.veracode.sca.scanresult.domain.Scan;
import com.veracode.sca.scanresult.platform.domain.Workspace;
import com.veracode.sca.scanresult.service.WorkspaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

/**
 * Rest controller for Sourceclear Workspaces.
 */
@Api(value = "SCA API for Sourceclear Workspaces")
@RestController
@RequestMapping(value = {"/v1", "/"}, produces = MediaType.APPLICATION_JSON_VALUE)
public class WorkspaceController {

    private final WorkspaceService workspaceService;

    public WorkspaceController(WorkspaceService workspaceService) {
        this.workspaceService = workspaceService;
    }

    /**
     * Return Sourceclear workspace Issues, based on the given workspace ID.
     *
     * @param workspaceId the workspace identifier
     * @return IssueSummary
     */
    @ApiOperation(value = "Get Issues", notes = "Returns workspace issues", response = Object.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Success response", response = Object.class)})
    @GetMapping(value = "/workspaces/{workspace_id}/issues")
    public ResponseEntity<Object> getIssues(@ApiParam(value = "Workspace ID",
            required = true) @PathVariable("workspace_id") final UUID workspaceId, Pageable pageable) {
        final Object issues = workspaceService.getWorkspaceIssues(workspaceId, pageable);
        return new ResponseEntity<>(issues, HttpStatus.OK);
    }
    /**
     * Return List of Sourceclear workspaces in the account.
     *
     * @return WorkspacesList
     */
    @ApiOperation(value = "Get Workspaces", notes = "Returns workspaces", response = Workspace.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "Success response", response = Scan.class)})
    @GetMapping(value = "/orgs/{org_id}/workspaces")
    public ResponseEntity<List<Workspace>> getWorkspaces(@ApiParam(value = "Org ID",
            required = true) @PathVariable("org_id") final long orgId) {

        final List<Workspace> workspacesList =
                workspaceService.getWorkspaces(orgId);
        return new ResponseEntity<>(workspacesList, HttpStatus.OK);
    }
}
