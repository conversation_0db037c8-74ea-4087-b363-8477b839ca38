package com.veracode.sca.scanresult.domain;

import java.io.Serializable;
import java.util.Objects;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import com.veracode.security.validation.VVGuid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;

/**
 * ShareScan identifier key
 *
 */
@ApiModel("Share scan")
@Embeddable
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ShareScanKey implements Serializable {
    private static final long serialVersionUID = 1L;

    private String scanId;

    private long appId;

    private long accountId;

    /**
     * Initialize ShareScanKey
     *
     * @param scanId Scan identifier
     * @param appId Application identifier
     * @param accountId Account identifier
     * @return ShareScanKey
     */
    public static ShareScanKey init(final String scanId, final long appId, final long accountId) {
        final ShareScanKey key = new ShareScanKey();
        key.scanId = scanId;
        key.appId = appId;
        key.accountId = accountId;
        return key;
    }

    /**
     * @return the scanId
     */
    public String getScanId() {
        return scanId;
    }

    /**
     * @return the appId
     */
    public long getAppId() {
        return appId;
    }

    /**
     * @return the accountId
     */
    public long getAccountId() {
        return accountId;
    }

    @SuppressWarnings({"PMD.CommentRequired"})
    @Override
    public int hashCode() {
        return Objects.hash(scanId, appId, accountId);
    }

    @SuppressWarnings({"PMD.CommentRequired"})
    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final ShareScanKey other = (ShareScanKey) o;
        return Objects.equals(this.scanId, other.scanId) && Objects.equals(this.accountId, other.accountId)
                && Objects.equals(this.appId, other.appId);
    }

    /**
     * @see Object#toString()
     */
    @Override
    public String toString() {
        return "ShareScanKey [scanId=" + scanId + ", accountId=" + accountId + ", appId=" + appId + "]";
    }
}
