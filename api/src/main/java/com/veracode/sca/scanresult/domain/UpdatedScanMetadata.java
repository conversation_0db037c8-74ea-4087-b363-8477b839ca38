/* © 2024 Veracode, Inc. */
package com.veracode.sca.scanresult.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class UpdatedScanMetadata {
  @JsonProperty private Long analysisUnitId;
  @JsonProperty private Long sandboxId;
  @JsonProperty private Long appVerId;

  public String toString() {
    return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
  }

  public Long getAnalysisUnitId() {
    return analysisUnitId;
  }

  public void setAnalysisUnitId(Long analysisUnitId) {
    this.analysisUnitId = analysisUnitId;
  }

  public Long getSandboxId() {
    return sandboxId;
  }

  public void setSandboxId(Long sandboxId) {
    this.sandboxId = sandboxId;
  }

  public Long getAppVerId() {
    return appVerId;
  }

  public void setAppVerId(Long appVerId) {
    this.appVerId = appVerId;
  }

  public static class Builder {
    private UpdatedScanMetadata scanMetadataChange;

    public Builder() {
      scanMetadataChange = new UpdatedScanMetadata();
    }

    public Builder withAnalysisUnitId(Long analysisUnitId) {
      scanMetadataChange.setAnalysisUnitId(analysisUnitId);
      return this;
    }

    public Builder withSandboxId(Long sandboxId) {
      scanMetadataChange.setSandboxId(sandboxId);
      return this;
    }

    public Builder withAppVerId(Long appVerId) {
      scanMetadataChange.setAppVerId(appVerId);
      return this;
    }

    public UpdatedScanMetadata build() {
      return scanMetadataChange;
    }
  }
}
