# Creates the deployment, unless the values file specifies it's a statefulset and deploymenDisabled is true
{{- if and (not .Values.StatefulSet) (not .Values.deploymentDisabled) }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "scanresult-processor.fullname" . }}
  labels:
{{ include "scanresult-processor.labels" . | indent 4 }} # typical labels, generated in _helpers.tpl
spec:
  {{- if .Values.progressDeadlineSeconds}}
  progressDeadlineSeconds: {{ .Values.progressDeadlineSeconds }}
  {{- end }}
  replicas: {{ .Values.replicaCount }}
{{- with .Values.deploymentStrategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
{{- end }}
{{- with .Values.volumeClaimTemplates }}
  volumeClaimTemplates:
  {{- toYaml . | nindent 2 }} # Adds the annotations
{{- end }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "scanresult-processor.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app: {{ .Values.app | default "o2"  }}
      name: {{ include "scanresult-processor.name" . }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "scanresult-processor.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app: {{ .Values.app | default "o2" }}
        name: {{ include "scanresult-processor.name" . }}
        {{- if .Values.podLabels }}
        {{- toYaml .Values.podLabels | nindent 8 }}
        {{- end }}
  {{- with .Values.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }} # Adds the annotations
    {{- end }}   
    spec:
      {{- if .Values.automountServiceAccountToken}}
      automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
      {{- end }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
        {{- with .Values.initContainers }} #if initContainers is added to the values file, it will use its definition. The tpl function will search and replace helm variables, if present in the code
      initContainers:
{{ tpl . $ | indent 8 }}
        {{- end }}

{{- if .Values.initContainerConfig.image}}
      initContainers:
        - name: {{ .Values.initContainerConfig.name | default .Values.initContainerConfig.image  }}
          image: "{{ .Values.dockerRepo }}/{{ .Values.initContainerConfig.image }}:{{ .Values.initContainerConfig.version }}"
          {{- with .Values.initContainerConfig.command }}
          command:
            {{- toYaml . | nindent 14 }}
            {{- end }}
          {{- with .Values.initContainerConfig.env | default .Values.valueFrom }}
          env:
          {{- toYaml . | nindent 14 }}
          {{- end }}

{{- end }}

{{- if .Values.hostname }}
      hostname: {{ .Values.hostname }}
{{ end }}
{{- if .Values.podManagementPolicy }}
      podManagementPolicy: {{ .Values.podManagementPolicy }}
{{ end }}
{{- if .Values.revisionHistoryLimit }}
      revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
{{ end }}

      containers:
        {{- with .Values.extraContainers }} # if extraContainers are defined tpl function will search and replace helm variables
{{ tpl . $ | indent 8 }}
        {{- end }}
        - name: {{ include "scanresult-processor.name" . }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.name }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.imageConfig.pullPolicy }}
          {{- with .Values.args }} # Adding arguments for the entrypoint script
          args:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.command }} # Command to be executed instead of the default entrypoint script (will replace helm variables)
          command:
{{ tpl . $ | indent 12 }}
          {{- end }}
          ports:
{{- if .Values.ports }} # If ports is not defined it will create a default http/80 port for the app. Otherwise it uses the ports definition
  {{- with .Values.ports }}
{{ tpl . $ | indent 12 }}
  {{- end }}
{{- else }}
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
{{- end }}
        {{- if .Values.securityContext}}
          securityContext:
            {{- if .Values.securityContext.runAsUser}}
            runAsUser: {{ .Values.securityContext.runAsUser }}
            {{- end }}
            {{- if .Values.securityContext.fsGroup}}
            fsGroup: {{ .Values.securityContext.fsGroup }}
            {{- end }}
            {{- if .Values.securityContext.privileged}}
            privileged: {{ .Values.securityContext.privileged }}
            {{- end }}
        {{- end }}
    {{- if .Values.env }}
          env:          
          {{- range $key, $val := .Values.env }}
            - name: {{ $key }}
              value: {{ $val | quote }}
          {{- end }}
    {{- with .Values.valueFrom }}
            {{- toYaml . | nindent 12 }}
    {{- end }}
    {{- end }}
    {{- if .Values.lifecycle }}
    {{- with .Values.lifecycle }}
          lifecycle:
    {{- toYaml . | nindent 12 }}
    {{- end }}
    {{- end }}
    {{- with .Values.livenessProbe }} # Liveness probe tells kubernetes the application is still running
          livenessProbe:
{{ tpl . $ | indent 12 }} 
    {{- end }}
    {{- with .Values.startupProbe }} # Tells kubernetes the application has started successfully
          startupProbe:
{{ tpl . $ | indent 12 }} 
    {{- end }}
    {{- with .Values.readinessProbe }} # Tells kubernetes the application has started successfully    
          readinessProbe:
{{ tpl . $ | indent 12 }}
    {{- end }}
    {{- with .Values.volumeMounts }} # Using the defined volumeMounts
          volumeMounts:
            {{- toYaml . | nindent 12 }}
    {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
    {{- if .Values.envFrom }}
          envFrom:
      {{- with .Values.envFrom }}
            {{ tpl . $ | nindent 12 }} # Adds environment variables, also replacing helm variables (if found in definition)
      {{- end }}
    {{- end }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.volumes }} # will define pod volumes, if defined in the values file.
      volumes:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if .Values.dnsPolicy }}
      dnsPolicy: {{ .Values.dnsPolicy }}
    {{- end }}
    {{- if .Values.restartPolicy }}
      restartPolicy: {{ .Values.restartPolicy }}
    {{- end }}
    {{- if .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
    {{- end }}
    {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
    {{- end }}
    {{- if .Values.serviceAccount }}
      serviceAccountName: {{ .Values.serviceAccount.name | default .Values.app }}
    {{- end }}      
    {{- if .Values.serviceAccountName }}
      serviceAccountName: {{ .Values.serviceAccountName }}
    {{- end }}      
{{- end }}

