{{- if .Values.serviceAccount }}
{{- $cn := include "scanresult-processor.fullname" . -}}
{{- $saName := .Values.serviceAccount.name | default $cn }} 
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
{{ include "scanresult-processor.labels" . | indent 4 }}    
  name: {{ $saName }}
  namespace: {{ .Release.Namespace }}
{{- if .Values.serviceAccount.annotations }}
  annotations:
  {{- toYaml .Values.serviceAccount.annotations | nindent 4 }}
{{- end }}
{{- if .Values.automountServiceAccountToken}}
automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
{{- end }}
{{- end }}

