apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "scanresult-processor.fullname" . }}-test-connection"
  labels:
{{ include "scanresult-processor.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "scanresult-processor.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never

