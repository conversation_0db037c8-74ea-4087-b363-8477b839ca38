nameOverride: scanresult-processor
fullnameOverride: scanresult-processor
replicaCount: 1

image:
  registry: "205744758777.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-sca-scanresult-service"
  tag: "ATL-3810-316382cf"

imageConfig:
  pullPolicy: IfNotPresent

imagePullSecrets: []
initContainerConfig: {}

secret:
  - name: sca-scanresult-processor-secrets
    type: Opaque
    data:
      SERVER_SSL_KEY_STORE_PASSWORD: Y2hhbmdlaXQ=
      SERVER_SSL_TRUST_STORE_PASSWORD: Y2hhbmdlaXQ=
      SPRING_DATASOURCE_READONLYDB_PASSWORD: c2NhdXNFcjAx
      SPRING_DATASOURCE_READONLYDB_USERNAME: c2NhdXNlcg==
      SPRING_DATASOURCE_READWRITEDB_PASSWORD: c2NhdXNFcjAx
      SPRING_DATASOURCE_READWRITEDB_USERNAME: c2NhdXNlcg==
  - name: sca-scanresult-processor-certs
    type: Opaque
    data:
      agora-identityservice-principal-signing-1.pem: LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFwRVNaTjJCemRrUE9FMERkc1FUdQp0cnZRMEsxYVpWYmg0cTFrQU1lZU1IUGc1VW1TUDk5T0diR0pyRUFTRU9qcXlvTmtsTS9JSEhjM1M4dm5PbWhNCjlkSm90a3I2Lzh4VkxsRDVweGtjczJRa0hGOGgxZmkxQ1l5VDVYS1JhS1k5VDdLcnE5UitJeWlQYWpFNHJpaEEKTkgvSkNtdks1UGs5OVIzWTFmclVVeXNRaUtHUTFQS0xJZWxjYWZkZS9pL1RBbzBEV0JiYjRZb3RHY1BmRGdZdQoyS1BaN29mR25jRVBReGJiby9LSVJIY2dhNUdrVUNSRTlyWkF0T0hoSjdobW0wUktkSVYvZ1YyUWVKdG0rWlIzClA0YnFaMGVqYWRzU3NrazBScnRwNlpocFZ0QWVjNE15QXNmVDdnVGdyZEFHZU9qTDJTTTRxUkppQUE0aDc4djgKWlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==
      sca-scanresult-service-keystore.jks: 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
      sca-scanresult-signing.pem: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      truststore.jks: 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
    
env:
  AGORA_SERVER_SSL_CLIENTS_ALLOWED: '*'
  APPLICATION_JMS_DATA_UPDATE_ENABLED: "true"
  APPLICATION_JMS_DATA_UPDATE_QUEUE: stage-132-data-update
  APPLICATION_JMS_ENABLED: "true"
  APPLICATION_JMS_POLICY_EVENT_ENABLED: "true"
  APPLICATION_JMS_POLICY_EVENT_QUEUE: stage-132-policy-event
  APPLICATION_JMS_RESULT_READY_ENABLED: "true"
  APPLICATION_JMS_RESULT_READY_QUEUE: stage-132-upload-result-ready
  APPLICATION_JMS_SCAN_EVENT_ENABLED: "true"
  APPLICATION_JMS_SCAN_UPDATE_EVENT_ENABLED: "false"
  APPLICATION_JMS_SCAN_UPDATE_EVENT_QUEUE: stage-132-sca-scan-event-srm
  APPLICATION_JMS_SNS_ENABLED: "true"
  APPLICATION_SCHEDULED_JOB_ENABLED: "true"
  APPLICATION_SERVICES_POLICY_BACKEND_URL: https://policy-backend
  AWS_SECRETSMANAGER_ENABLED: "false"
  DD_PROFILING_ENABLED: false
  DD_SERVICE: sca-scanresult-processor
  DD_TAGS: env:stage stack_name:stage-dyn-tachyon
  JAVA_TOOL_OPTIONS: -javaagent:/datadog/dd-java-agent.jar
  LOGGING_LEVEL_COM_DATADOG_PROFILING_UPLOADER_PROFILEUPLOADER: FATAL
  LOGGING_LEVEL_COM_VERACODE_AGORA_SERVICEBASE_RESTCALLINTERCEPTOR: FATAL
  LOGGING_LEVEL_COM_ZAXXER_HIKARI_POOL_HIKARIPOOL: INFO
  LOGGING_LEVEL_DATADOG_TRACE_AGENT_COMMON_WRITER_DDAGENT_DDAGENTAPI: FATAL
  PLATFORM_API_URL: http://platform-backend
  PLATFORM_SIGN_KEY_FILE: sca-scanresult-signing.pem
  REGISTRY_API_URL: http://registry-api
  SERVER_SSL_CLIENT_AUTH: want
  SERVER_SSL_KEY_STORE: file:config/sca-scanresult-service-keystore.jks
  SERVER_SSL_TRUST_STORE: file:config/truststore.jks
  SPRING_CACHE_TYPE: NONE
  SPRING_DATASOURCE_READONLYDB_URL: ***********************************************************************************************************************************
  SPRING_DATASOURCE_READWRITEDB_URL: ********************************************************************************************************************************
  SPRING_REDIS_HOST: sca-redis-primary
  SPRING_REDIS_PORT: "6379"


valueFrom:
  - name: POD_NAMESPACE
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace

envFrom: |
  - secretRef:
      name: sca-scanresult-processor-secrets

volumes:
  - name: sca-scanresult-cert-mount
    secret:
      defaultMode: 420
      secretName: sca-scanresult-processor-certs

volumeMounts:
  - mountPath: /data/config/agora-identityservice-principal-signing-1.pem
    name: sca-scanresult-cert-mount
    readOnly: true
    subPath: agora-identityservice-principal-signing-1.pem
  - mountPath: /data/config/sca-scanresult-signing.pem
    name: sca-scanresult-cert-mount
    readOnly: true
    subPath: sca-scanresult-signing.pem
  - mountPath: /data/config/sca-scanresult-service-keystore.jks
    name: sca-scanresult-cert-mount
    readOnly: true
    subPath: sca-scanresult-service-keystore.jks
  - mountPath: /data/config/truststore.jks
    name: sca-scanresult-cert-mount
    readOnly: true
    subPath: truststore.jks

readinessProbe: |
  httpGet:
    path: /sca/scanresult/actuator/health
    port: actuator
  initialDelaySeconds: 90
livenessProbe: |
  httpGet:
    path: /sca/scanresult/actuator/health
    port: actuator
  initialDelaySeconds: 90

service:
  type: ClusterIP
  port: 80

resources:
  requests:
    cpu: 256m
    memory: 512Mi

automountServiceAccountToken: true

progressDeadlineSeconds: 180

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0
    
command: "[java]"
args: [-jar, /data/sca-scanresult-service.jar]

nodeSelector: {}
tolerations: []
affinity: {}

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/scanresult-processor-dev-dyn-tachyon-serviceaccount
  name: scanresult-processor